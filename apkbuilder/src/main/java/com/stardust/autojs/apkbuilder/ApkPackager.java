package com.stardust.autojs.apkbuilder;

import android.text.TextUtils;

import com.stardust.autojs.apkbuilder.util.StreamUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import pxb.android.tinysign.TinySign;

/**
 * Created by Stardust on 2017/10/23.
 */

public class ApkPackager {

    private InputStream mApkInputStream;
    private String mWorkspacePath;

    public ApkPackager(InputStream apkInputStream, String workspacePath) {
        mApkInputStream = apkInputStream;
        mWorkspacePath = workspacePath;
    }

    public ApkPackager(String apkPath, String workspacePath) throws FileNotFoundException {
        mApkInputStream = new FileInputStream(apkPath);
        mWorkspacePath = workspacePath;
    }

    public void unzip() throws IOException {
        try (ZipInputStream zis = new ZipInputStream(mApkInputStream)) {
            for (ZipEntry e = zis.getNextEntry(); e != null; e = zis.getNextEntry()) {
                String name = e.getName();
                if (!e.isDirectory() && !TextUtils.isEmpty(name)) {
                    File file = new File(mWorkspacePath, name);
                    System.out.println(file);
                    file.getParentFile().mkdirs();
                    FileOutputStream fos = new FileOutputStream(file);
                    StreamUtils.write(zis, fos);
                    fos.close();
                }
            }
        }
    }

    public void repackage(String newApkPath) throws Exception {
        FileOutputStream fos = new FileOutputStream(newApkPath);
        // Use custom signing that handles Android R+ requirements
        signWithAlignment(new File(mWorkspacePath), fos);
        fos.close();
    }

    private void signWithAlignment(File dir, FileOutputStream out) throws Exception {
        // Remove old signature files
        File metaInfDir = new File(dir, "META-INF");
        if (metaInfDir.exists()) {
            File[] files = metaInfDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.getName().matches(".*\\.(MF|SF|RSA)$")) {
                        file.delete();
                    }
                }
            }
        }

        // Use TinySign but ensure proper handling
        TinySign.sign(dir, out);
    }

    public void cleanWorkspace() {

    }

}
