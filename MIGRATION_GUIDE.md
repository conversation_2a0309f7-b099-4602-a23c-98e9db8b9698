# AutoX.js APK构建系统迁移指南

## 概述

本指南说明如何从旧的APK构建系统迁移到新的`EmbeddedApkBuilder`系统，以解决Android R+ (API 30+) 的兼容性问题。

## 迁移步骤

### 1. 备份现有代码

在开始迁移之前，建议备份以下关键文件：
- `app/src/main/java/org/autojs/autojs/build/ApkBuilder.kt`
- `app/src/main/java/org/autojs/autojs/ui/build/BuildViewModel.kt`
- `apkbuilder/src/main/java/com/stardust/autojs/apkbuilder/ApkPackager.java`

### 2. 添加新文件

已创建的新文件：
- `app/src/main/java/org/autojs/autojs/build/EmbeddedApkBuilder.kt` - 新的构建器
- `app/src/main/java/org/autojs/autojs/util/ProcessUtils.kt` - 进程工具类
- `app/src/test/java/org/autojs/autojs/build/ApkStructureTest.kt` - APK结构验证测试

### 3. 修改BuildViewModel

已经修改了`BuildViewModel.kt`中的`callApkBuilder`方法，使用新的`EmbeddedApkBuilder`：

```kotlin
// 旧代码
val apkBuilder = ApkBuilder(templateApk, outApk, tmpDir.path)

// 新代码
val apkBuilder = EmbeddedApkBuilder(app, templateApk, outApk, tmpDir)
```

### 4. 验证构建配置

确保以下构建配置正确：

#### app/build.gradle.kts
```kotlin
android {
    androidResources {
        noCompress += ".arsc"
    }
}
```

#### inrt/build.gradle.kts
```kotlin
android {
    androidResources {
        noCompress += ".arsc"
    }
}
```

### 5. 测试新系统

#### 基本功能测试
1. 创建一个简单的测试脚本
2. 使用新系统构建APK
3. 在Android 11+设备上安装测试

#### APK结构验证
使用提供的测试工具验证APK结构：

```kotlin
val verifier = ApkStructureTest()
val result = verifier.verifyApkStructure(File("path/to/your.apk"))
verifier.printVerificationResult(result)
```

#### 命令行验证
```bash
# 检查resources.arsc是否未压缩
unzip -l your_app.apk | grep resources.arsc

# 验证APK签名
apksigner verify --verbose your_app.apk

# 检查4字节对齐
zipalign -c -v 4 your_app.apk
```

## 关键改进

### 1. Android R+兼容性

**旧系统问题**：
- 手动处理resources.arsc压缩和对齐
- 多套签名实现，兼容性不一致
- 容易出现对齐错误

**新系统解决方案**：
```kotlin
// 自动处理Android R+要求
if (entryName == "resources.arsc") {
    entry.method = ZipEntry.STORED  // 未压缩
    val fileBytes = file.readBytes()
    entry.size = fileBytes.size.toLong()
    
    val crc32 = CRC32()
    crc32.update(fileBytes)
    entry.crc = crc32.value  // 正确的CRC32
}
```

### 2. 统一签名方案

**旧系统**：
- ZipSigner（支持Android R+）
- DefaultSign（部分支持）
- TinySign（不支持Android R+）

**新系统**：
- 只使用ZipSigner，确保一致性
- 支持SHA256withRSA签名算法
- 自动处理证书和密钥管理

### 3. 简化构建流程

**旧流程**：
```
解压模板APK → 修改文件 → 重新打包 → 多次签名尝试
```

**新流程**：
```
解压模板APK → 修改文件 → 优化打包 → 统一签名
```

## 回滚方案

如果新系统出现问题，可以快速回滚：

### 1. 恢复BuildViewModel
```kotlin
// 在callApkBuilder方法中恢复旧代码
val apkBuilder = ApkBuilder(templateApk, outApk, tmpDir.path)
```

### 2. 移除新文件
- 删除或重命名`EmbeddedApkBuilder.kt`
- 移除相关导入

### 3. 验证回滚
确保旧系统仍然可以正常工作

## 故障排除

### 常见问题

#### 1. "Template APK not found"
- 检查inrt模块是否正确构建
- 验证template.apk是否存在于assets目录

#### 2. "Keystore not found"
- 确保autox-release.keystore存在于assets目录
- 检查用户自定义keystore路径

#### 3. "APK installation failed"
- 使用APK结构验证工具检查
- 确认resources.arsc未压缩
- 验证APK签名

#### 4. 构建性能问题
- 检查工作目录清理
- 监控内存使用情况
- 考虑并行化处理

### 调试技巧

#### 1. 启用详细日志
```kotlin
// 在EmbeddedApkBuilder中添加更多日志
Log.d("EmbeddedApkBuilder", "Processing file: $entryName")
```

#### 2. 保留中间文件
```kotlin
// 临时禁用工作目录清理进行调试
// apkBuilder.cleanWorkspace()
```

#### 3. 手动验证APK
```bash
# 解压APK查看结构
unzip -d extracted_apk your_app.apk
ls -la extracted_apk/
```

## 性能优化

### 1. 文件I/O优化
- 使用缓冲流
- 减少文件复制次数
- 并行处理非依赖文件

### 2. 内存管理
- 及时关闭流
- 清理临时文件
- 监控内存使用

### 3. 构建缓存
- 缓存模板APK解压结果
- 增量构建支持
- 资源文件缓存

## 后续计划

1. **监控和反馈**：收集用户使用反馈
2. **性能优化**：持续改进构建速度
3. **功能扩展**：支持更多APK优化选项
4. **自动化测试**：增加更多自动化验证
5. **文档完善**：补充使用文档和最佳实践

## 联系支持

如果在迁移过程中遇到问题：
1. 检查日志输出
2. 使用提供的验证工具
3. 参考故障排除部分
4. 必要时使用回滚方案
