package org.autojs.autojs.build

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.withContext
import org.autojs.autojs.model.project.ProjectConfig
import org.autojs.autojs.util.ProcessUtils
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipFile
import java.util.zip.ZipOutputStream

/**
 * 现代化的APK构建器，使用Android官方工具链
 * 
 * 构建流程：
 * 1. 解压模板APK到工作目录
 * 2. 修改AndroidManifest.xml和资源文件
 * 3. 复制用户脚本到assets目录
 * 4. 使用aapt2重新打包资源
 * 5. 使用zipalign对齐APK
 * 6. 使用apksigner签名APK
 */
class ModernApkBuilder(
    private val context: Context,
    private val templateApk: InputStream,
    private val outputApk: File,
    private val workspaceDir: File
) {
    private val _progressState = MutableSharedFlow<BuildState>()
    val progressState: SharedFlow<BuildState> = _progressState
    
    private var projectConfig: ProjectConfig? = null
    private val manifestFile get() = File(workspaceDir, "AndroidManifest.xml")
    private val resourcesDir get() = File(workspaceDir, "res")
    private val assetsDir get() = File(workspaceDir, "assets")
    private val libDir get() = File(workspaceDir, "lib")
    
    // Android SDK工具路径
    private val androidSdkRoot = getAndroidSdkPath()
    private val aapt2 = File(androidSdkRoot, "build-tools/${getBuildToolsVersion()}/aapt2")
    private val zipalign = File(androidSdkRoot, "build-tools/${getBuildToolsVersion()}/zipalign")
    private val apksigner = File(androidSdkRoot, "build-tools/${getBuildToolsVersion()}/apksigner")
    
    suspend fun prepare(): ModernApkBuilder {
        _progressState.emit(BuildState.PREPARE)
        withContext(Dispatchers.IO) {
            // 清理并创建工作目录
            if (workspaceDir.exists()) {
                workspaceDir.deleteRecursively()
            }
            workspaceDir.mkdirs()
            
            // 解压模板APK
            extractTemplateApk()
            
            // 验证Android SDK工具
            validateSdkTools()
        }
        return this
    }
    
    suspend fun withConfig(config: ProjectConfig): ModernApkBuilder {
        this.projectConfig = config
        withContext(Dispatchers.IO) {
            // 修改AndroidManifest.xml
            updateManifest(config)
            
            // 复制脚本文件
            copyScriptFiles(config)
            
            // 处理图标资源
            updateIcons(config)
        }
        return this
    }
    
    suspend fun build(): ModernApkBuilder {
        _progressState.emit(BuildState.BUILD)
        withContext(Dispatchers.IO) {
            val config = projectConfig ?: throw IllegalStateException("Project config not set")
            
            // 使用aapt2编译资源
            compileResources()
            
            // 创建未签名的APK
            createUnsignedApk()
        }
        return this
    }
    
    suspend fun sign(keystorePath: String?, keyPassword: String?): ModernApkBuilder {
        _progressState.emit(BuildState.SIGN)
        withContext(Dispatchers.IO) {
            val unsignedApk = File(workspaceDir, "unsigned.apk")
            val alignedApk = File(workspaceDir, "aligned.apk")
            
            // 1. 使用zipalign对齐APK（这会自动处理Android R+的对齐要求）
            alignApk(unsignedApk, alignedApk)
            
            // 2. 使用apksigner签名APK
            signApk(alignedApk, outputApk, keystorePath, keyPassword)
        }
        return this
    }
    
    suspend fun finish() {
        _progressState.emit(BuildState.FINISH)
        // 清理工作目录
        withContext(Dispatchers.IO) {
            workspaceDir.deleteRecursively()
        }
    }
    
    private fun extractTemplateApk() {
        ZipFile(templateApk.use { input ->
            val tempFile = File.createTempFile("template", ".apk")
            tempFile.deleteOnExit()
            FileOutputStream(tempFile).use { output ->
                input.copyTo(output)
            }
            tempFile
        }).use { zipFile ->
            zipFile.entries().asSequence().forEach { entry ->
                if (!entry.isDirectory) {
                    val outputFile = File(workspaceDir, entry.name)
                    outputFile.parentFile?.mkdirs()
                    
                    zipFile.getInputStream(entry).use { input ->
                        FileOutputStream(outputFile).use { output ->
                            input.copyTo(output)
                        }
                    }
                }
            }
        }
    }
    
    private fun updateManifest(config: ProjectConfig) {
        // 使用现有的ManifestEditor或创建新的XML处理逻辑
        val manifestEditor = ManifestEditor(manifestFile)
        manifestEditor.setPackageName(config.packageName)
        manifestEditor.setAppName(config.name)
        manifestEditor.setVersionName(config.versionName)
        manifestEditor.setVersionCode(config.versionCode)
        manifestEditor.commit()
        manifestEditor.writeTo(FileOutputStream(manifestFile))
    }
    
    private fun copyScriptFiles(config: ProjectConfig) {
        val projectDir = File(config.projectDirectory)
        val targetDir = File(assetsDir, "project")
        
        if (projectDir.isDirectory) {
            projectDir.copyRecursively(targetDir, overwrite = true)
        } else {
            // 单文件脚本
            targetDir.mkdirs()
            projectDir.copyTo(File(targetDir, config.mainScript), overwrite = true)
        }
    }
    
    private fun updateIcons(config: ProjectConfig) {
        // 处理应用图标和启动图标
        config.icon?.let { iconPath ->
            // 复制图标到相应的drawable目录
            // 这里需要根据实际的资源结构来实现
        }
    }
    
    private fun compileResources() {
        // 使用aapt2编译资源
        val compiledResDir = File(workspaceDir, "compiled_res")
        compiledResDir.mkdirs()
        
        // 编译资源文件
        val compileCmd = listOf(
            aapt2.absolutePath,
            "compile",
            "--dir", resourcesDir.absolutePath,
            "-o", compiledResDir.absolutePath
        )
        
        ProcessUtils.executeCommand(compileCmd, workspaceDir)
        
        // 链接资源
        val linkedApk = File(workspaceDir, "resources.apk")
        val linkCmd = listOf(
            aapt2.absolutePath,
            "link",
            "--proto-format",
            "-o", linkedApk.absolutePath,
            "-I", getAndroidJarPath(),
            "--manifest", manifestFile.absolutePath,
            "--auto-add-overlay",
            compiledResDir.absolutePath
        )
        
        ProcessUtils.executeCommand(linkCmd, workspaceDir)
    }
    
    private fun createUnsignedApk() {
        val resourcesApk = File(workspaceDir, "resources.apk")
        val unsignedApk = File(workspaceDir, "unsigned.apk")
        
        // 将资源APK作为基础，添加assets和lib目录
        ZipFile(resourcesApk).use { resourcesZip ->
            ZipOutputStream(FileOutputStream(unsignedApk)).use { output ->
                // 复制资源文件
                resourcesZip.entries().asSequence().forEach { entry ->
                    if (!entry.isDirectory) {
                        output.putNextEntry(ZipEntry(entry.name))
                        resourcesZip.getInputStream(entry).copyTo(output)
                        output.closeEntry()
                    }
                }
                
                // 添加assets目录
                if (assetsDir.exists()) {
                    addDirectoryToZip(output, assetsDir, "assets/")
                }
                
                // 添加lib目录
                if (libDir.exists()) {
                    addDirectoryToZip(output, libDir, "lib/")
                }
            }
        }
    }
    
    private fun addDirectoryToZip(zipOutput: ZipOutputStream, dir: File, prefix: String) {
        dir.walkTopDown().forEach { file ->
            if (file.isFile) {
                val relativePath = prefix + file.relativeTo(dir).path.replace('\\', '/')
                zipOutput.putNextEntry(ZipEntry(relativePath))
                file.inputStream().use { it.copyTo(zipOutput) }
                zipOutput.closeEntry()
            }
        }
    }
    
    private fun alignApk(inputApk: File, outputApk: File) {
        val alignCmd = listOf(
            zipalign.absolutePath,
            "-f", "4",  // 4字节对齐，自动处理Android R+要求
            inputApk.absolutePath,
            outputApk.absolutePath
        )
        
        ProcessUtils.executeCommand(alignCmd, workspaceDir)
    }
    
    private fun signApk(inputApk: File, outputApk: File, keystorePath: String?, keyPassword: String?) {
        val signCmd = if (!keystorePath.isNullOrEmpty() && !keyPassword.isNullOrEmpty()) {
            // 使用用户提供的keystore
            listOf(
                apksigner.absolutePath,
                "sign",
                "--ks", keystorePath,
                "--ks-pass", "pass:$keyPassword",
                "--out", outputApk.absolutePath,
                inputApk.absolutePath
            )
        } else {
            // 使用默认keystore
            val defaultKeystore = extractDefaultKeystore()
            listOf(
                apksigner.absolutePath,
                "sign",
                "--ks", defaultKeystore.absolutePath,
                "--ks-pass", "pass:autox123456",
                "--out", outputApk.absolutePath,
                inputApk.absolutePath
            )
        }
        
        ProcessUtils.executeCommand(signCmd, workspaceDir)
    }
    
    private fun extractDefaultKeystore(): File {
        val keystoreFile = File(context.filesDir, "autox-release.keystore")
        if (!keystoreFile.exists()) {
            context.assets.open("autox-release.keystore").use { input ->
                FileOutputStream(keystoreFile).use { output ->
                    input.copyTo(output)
                }
            }
        }
        return keystoreFile
    }
    
    private fun validateSdkTools() {
        if (!aapt2.exists()) throw IllegalStateException("aapt2 not found: ${aapt2.absolutePath}")
        if (!zipalign.exists()) throw IllegalStateException("zipalign not found: ${zipalign.absolutePath}")
        if (!apksigner.exists()) throw IllegalStateException("apksigner not found: ${apksigner.absolutePath}")
    }
    
    private fun getAndroidSdkPath(): String {
        // 尝试从环境变量获取Android SDK路径
        return System.getenv("ANDROID_SDK_ROOT") 
            ?: System.getenv("ANDROID_HOME")
            ?: "/opt/android-sdk"  // 默认路径
    }
    
    private fun getBuildToolsVersion(): String {
        // 返回支持的build-tools版本
        return "34.0.0"  // 或者动态检测最新版本
    }
    
    private fun getAndroidJarPath(): String {
        return "$androidSdkRoot/platforms/android-34/android.jar"
    }
}
