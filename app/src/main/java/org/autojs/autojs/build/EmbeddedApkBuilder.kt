package org.autojs.autojs.build

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.withContext
import com.stardust.autojs.project.ProjectConfig
import org.autojs.autojs.build.apksigner.ZipSigner
import com.stardust.autojs.apkbuilder.ManifestEditor
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream
import java.security.KeyStore
import java.security.PrivateKey
import java.security.cert.X509Certificate
import java.util.zip.ZipEntry
import java.util.zip.ZipFile
import java.util.zip.ZipOutputStream
import java.util.zip.CRC32

/**
 * 内嵌式APK构建器 - 不依赖外部Android SDK工具
 * 
 * 特点：
 * 1. 使用内嵌的签名工具，已经支持Android R+
 * 2. 正确处理resources.arsc的压缩和对齐
 * 3. 简化的构建流程，减少中间步骤
 * 4. 更好的错误处理和日志记录
 */
class EmbeddedApkBuilder(
    private val context: Context,
    private val templateApk: InputStream,
    private val outputApk: File,
    private val workspaceDir: File
) {
    private val _progressState = MutableSharedFlow<Int>()
    val progressState: SharedFlow<Int> = _progressState

    object BuildState {
        const val PREPARE = 0
        const val BUILD = 1
        const val SIGN = 2
        const val CLEAN = 3
        const val FINISH = 4
    }
    
    private var projectConfig: ProjectConfig? = null
    private val manifestFile get() = File(workspaceDir, "AndroidManifest.xml")
    private val assetsDir get() = File(workspaceDir, "assets")
    private val libDir get() = File(workspaceDir, "lib")
    private val tempApkFile get() = File(workspaceDir, "temp.apk")
    
    suspend fun prepare(): EmbeddedApkBuilder {
        _progressState.emit(BuildState.PREPARE)
        withContext(Dispatchers.IO) {
            // 清理并创建工作目录
            if (workspaceDir.exists()) {
                workspaceDir.deleteRecursively()
            }
            workspaceDir.mkdirs()
            
            // 解压模板APK到工作目录
            extractTemplateApk()
        }
        return this
    }
    
    suspend fun withConfig(config: ProjectConfig): EmbeddedApkBuilder {
        this.projectConfig = config
        withContext(Dispatchers.IO) {
            // 修改AndroidManifest.xml
            updateManifest(config)
            
            // 复制脚本文件
            copyScriptFiles(config)
            
            // 处理图标资源
            updateIcons(config)
        }
        return this
    }
    
    suspend fun build(): EmbeddedApkBuilder {
        _progressState.emit(BuildState.BUILD)
        withContext(Dispatchers.IO) {
            // 创建优化的APK，正确处理Android R+要求
            createOptimizedApk()
        }
        return this
    }
    
    suspend fun sign(keystorePath: String?, keyPassword: String?): EmbeddedApkBuilder {
        _progressState.emit(BuildState.SIGN)
        withContext(Dispatchers.IO) {
            if (!keystorePath.isNullOrEmpty() && !keyPassword.isNullOrEmpty()) {
                // 使用用户提供的keystore
                signWithUserKeystore(keystorePath, keyPassword)
            } else {
                // 使用默认keystore
                signWithDefaultKeystore()
            }
        }
        return this
    }
    
    suspend fun finish() {
        _progressState.emit(BuildState.FINISH)
    }
    
    fun cleanWorkspace() {
        if (workspaceDir.exists()) {
            workspaceDir.deleteRecursively()
        }
    }
    
    private fun extractTemplateApk() {
        // 先将InputStream保存为临时文件
        val tempTemplateFile = File.createTempFile("template", ".apk")
        tempTemplateFile.deleteOnExit()
        
        templateApk.use { input ->
            FileOutputStream(tempTemplateFile).use { output ->
                input.copyTo(output)
            }
        }
        
        // 解压APK到工作目录
        ZipFile(tempTemplateFile).use { zipFile ->
            zipFile.entries().asSequence().forEach { entry ->
                if (!entry.isDirectory) {
                    val outputFile = File(workspaceDir, entry.name)
                    outputFile.parentFile?.mkdirs()
                    
                    zipFile.getInputStream(entry).use { input ->
                        FileOutputStream(outputFile).use { output ->
                            input.copyTo(output)
                        }
                    }
                }
            }
        }
        
        tempTemplateFile.delete()
    }
    
    private fun updateManifest(config: ProjectConfig) {
        val manifestEditor = ManifestEditor(FileInputStream(manifestFile))
        manifestEditor.setPackageName(config.packageName)
        manifestEditor.setAppName(config.name)
        manifestEditor.setVersionName(config.versionName)
        manifestEditor.setVersionCode(config.versionCode)

        manifestEditor.commit()
        manifestEditor.writeTo(FileOutputStream(manifestFile))
    }
    
    private fun copyScriptFiles(config: ProjectConfig) {
        val projectDir = File(config.projectDirectory)
        val targetDir = File(assetsDir, "project")
        
        // 确保目标目录存在
        targetDir.mkdirs()
        
        if (projectDir.isDirectory) {
            // 复制整个项目目录
            projectDir.copyRecursively(targetDir, overwrite = true)
        } else {
            // 单文件脚本
            projectDir.copyTo(File(targetDir, config.mainScript), overwrite = true)
        }
    }
    
    private fun updateIcons(config: ProjectConfig) {
        // 处理应用图标
        config.icon?.let { iconPath ->
            val iconFile = File(config.projectDirectory).parentFile?.resolve(iconPath)
            if (iconFile?.exists() == true) {
                // 复制到各种分辨率的drawable目录
                val drawableDirs = listOf("drawable", "drawable-hdpi", "drawable-xhdpi", "drawable-xxhdpi")
                drawableDirs.forEach { dir ->
                    val targetDir = File(workspaceDir, "res/$dir")
                    if (targetDir.exists()) {
                        iconFile.copyTo(File(targetDir, "ic_launcher.png"), overwrite = true)
                    }
                }
            }
        }
        
        // 处理启动图标
        config.launchConfig.splashIcon?.let { splashIconPath ->
            val splashFile = File(config.projectDirectory).parentFile?.resolve(splashIconPath)
            if (splashFile?.exists() == true) {
                val targetDir = File(workspaceDir, "res/drawable")
                if (targetDir.exists()) {
                    splashFile.copyTo(File(targetDir, "autojs_splash.png"), overwrite = true)
                }
            }
        }
    }
    
    private fun createOptimizedApk() {
        ZipOutputStream(FileOutputStream(tempApkFile)).use { zipOut ->
            // 设置压缩级别
            zipOut.setLevel(9)
            
            // 遍历工作目录中的所有文件
            workspaceDir.walkTopDown().forEach { file ->
                if (file.isFile && file != tempApkFile) {
                    val relativePath = file.relativeTo(workspaceDir).path.replace('\\', '/')
                    addFileToZip(zipOut, file, relativePath)
                }
            }
        }
    }
    
    private fun addFileToZip(zipOut: ZipOutputStream, file: File, entryName: String) {
        val entry = ZipEntry(entryName)
        
        // Android R+ 特殊处理：resources.arsc必须未压缩且4字节对齐
        if (entryName == "resources.arsc") {
            entry.method = ZipEntry.STORED
            val fileBytes = file.readBytes()
            entry.size = fileBytes.size.toLong()
            
            // 计算CRC32
            val crc32 = CRC32()
            crc32.update(fileBytes)
            entry.crc = crc32.value
            
            zipOut.putNextEntry(entry)
            zipOut.write(fileBytes)
        } else {
            // 其他文件正常压缩
            entry.method = ZipEntry.DEFLATED
            zipOut.putNextEntry(entry)
            file.inputStream().use { input ->
                input.copyTo(zipOut)
            }
        }
        
        zipOut.closeEntry()
    }
    
    private fun signWithUserKeystore(keystorePath: String, keyPassword: String) {
        val keystoreFile = File(keystorePath)
        if (!keystoreFile.exists()) {
            throw IllegalArgumentException("Keystore file not found: $keystorePath")
        }
        
        try {
            val keyStore = KeyStore.getInstance("JKS")
            FileInputStream(keystoreFile).use { fis ->
                keyStore.load(fis, keyPassword.toCharArray())
            }
            
            val alias = keyStore.aliases().nextElement()
            val privateKey = keyStore.getKey(alias, keyPassword.toCharArray()) as PrivateKey
            val certificate = keyStore.getCertificate(alias) as X509Certificate
            
            // 使用ZipSigner签名（已支持Android R+）
            ZipSigner.signZip(
                certificate,
                privateKey,
                "SHA256withRSA",
                tempApkFile.absolutePath,
                outputApk.absolutePath
            )
        } catch (e: Exception) {
            throw RuntimeException("Failed to sign APK with user keystore", e)
        }
    }
    
    private fun signWithDefaultKeystore() {
        try {
            // 首先尝试从assets中提取默认keystore
            val keystoreFile = File(context.filesDir, "autox-release.keystore")
            if (!keystoreFile.exists()) {
                try {
                    context.assets.open("autox-release.keystore").use { input ->
                        FileOutputStream(keystoreFile).use { output ->
                            input.copyTo(output)
                        }
                    }
                } catch (e: Exception) {
                    // 如果找不到keystore文件，使用DefaultSign作为回退
                    FileOutputStream(outputApk).use { output ->
                        DefaultSign.sign(tempApkFile.parentFile, output)
                    }
                    return
                }
            }

            val keyStore = KeyStore.getInstance("JKS")
            FileInputStream(keystoreFile).use { fis ->
                keyStore.load(fis, "autox123456".toCharArray())
            }

            val alias = keyStore.aliases().nextElement()
            val privateKey = keyStore.getKey(alias, "autox123456".toCharArray()) as PrivateKey
            val certificate = keyStore.getCertificate(alias) as X509Certificate

            // 使用ZipSigner签名
            ZipSigner.signZip(
                certificate,
                privateKey,
                "SHA256withRSA",
                tempApkFile.absolutePath,
                outputApk.absolutePath
            )
        } catch (e: Exception) {
            // 如果keystore签名失败，使用DefaultSign作为最终回退
            try {
                FileOutputStream(outputApk).use { output ->
                    DefaultSign.sign(tempApkFile.parentFile, output)
                }
            } catch (fallbackException: Exception) {
                throw RuntimeException("Failed to sign APK with both keystore and default methods", fallbackException)
            }
        }
    }
}
