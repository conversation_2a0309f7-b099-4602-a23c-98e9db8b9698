package org.autojs.autojs.util

import android.util.Log
import java.io.File
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 进程执行工具类
 */
object ProcessUtils {
    private const val TAG = "ProcessUtils"
    
    /**
     * 执行命令行工具
     * @param command 命令和参数列表
     * @param workingDir 工作目录
     * @param timeoutSeconds 超时时间（秒）
     * @return 执行结果
     */
    fun executeCommand(
        command: List<String>,
        workingDir: File? = null,
        timeoutSeconds: Long = 300
    ): ProcessResult {
        Log.d(TAG, "Executing command: ${command.joinToString(" ")}")
        
        return try {
            val processBuilder = ProcessBuilder(command)
            workingDir?.let { processBuilder.directory(it) }
            
            // 合并错误输出到标准输出
            processBuilder.redirectErrorStream(true)
            
            val process = processBuilder.start()
            
            // 读取输出
            val output = process.inputStream.bufferedReader().use { it.readText() }
            
            // 等待进程完成
            val finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS)
            
            if (!finished) {
                process.destroyForcibly()
                throw IOException("Command timed out after $timeoutSeconds seconds")
            }
            
            val exitCode = process.exitValue()
            Log.d(TAG, "Command finished with exit code: $exitCode")
            
            if (output.isNotEmpty()) {
                Log.d(TAG, "Command output: $output")
            }
            
            ProcessResult(exitCode, output, exitCode == 0)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to execute command: ${command.joinToString(" ")}", e)
            ProcessResult(-1, e.message ?: "Unknown error", false)
        }
    }
    
    /**
     * 检查命令是否可用
     */
    fun isCommandAvailable(command: String): Boolean {
        return try {
            val result = executeCommand(listOf(command, "--version"), timeoutSeconds = 10)
            result.success
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 查找Android SDK路径
     */
    fun findAndroidSdk(): String? {
        // 尝试从环境变量获取
        System.getenv("ANDROID_SDK_ROOT")?.let { return it }
        System.getenv("ANDROID_HOME")?.let { return it }
        
        // 尝试常见路径
        val commonPaths = listOf(
            "/opt/android-sdk",
            "/usr/local/android-sdk",
            System.getProperty("user.home") + "/Android/Sdk",
            System.getProperty("user.home") + "/Library/Android/sdk"
        )
        
        return commonPaths.firstOrNull { File(it).exists() }
    }
    
    /**
     * 查找最新的build-tools版本
     */
    fun findLatestBuildTools(sdkPath: String): String? {
        val buildToolsDir = File(sdkPath, "build-tools")
        if (!buildToolsDir.exists()) return null
        
        return buildToolsDir.listFiles()
            ?.filter { it.isDirectory }
            ?.map { it.name }
            ?.sortedWith(VersionComparator())
            ?.lastOrNull()
    }
    
    /**
     * 版本号比较器
     */
    private class VersionComparator : Comparator<String> {
        override fun compare(v1: String, v2: String): Int {
            val parts1 = v1.split(".").map { it.toIntOrNull() ?: 0 }
            val parts2 = v2.split(".").map { it.toIntOrNull() ?: 0 }
            
            val maxLength = maxOf(parts1.size, parts2.size)
            
            for (i in 0 until maxLength) {
                val part1 = parts1.getOrNull(i) ?: 0
                val part2 = parts2.getOrNull(i) ?: 0
                
                when {
                    part1 < part2 -> return -1
                    part1 > part2 -> return 1
                }
            }
            
            return 0
        }
    }
}

/**
 * 进程执行结果
 */
data class ProcessResult(
    val exitCode: Int,
    val output: String,
    val success: Boolean
)
