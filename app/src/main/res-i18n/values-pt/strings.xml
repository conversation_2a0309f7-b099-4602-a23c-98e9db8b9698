<?xml version="1.0" encoding="UTF-8"?>

<!--Automatically translated by Globalization Translator (https://github.com/wilinz/globalization-translator)-->
<resources>
  <string name="text_accessibility_service_description">Necessário para automatizar as operações de script (clique, toque longo, deslize, etc.), se desabilitado, somente scripts que não envolvem operações automáticas podem ser executados.</string>
  <string name="text_new_file">Crie um novo arquivo</string>
  <string name="text_create_fail">Falha ao criar</string>
  <string name="text_please_input_name">Por favor, digite um nome</string>
  <string name="text_name">Nome</string>
  <string name="text_go_to_setting">Vá para as configurações</string>
  <string name="explain_accessibility_permission">O software precisa abrir \"Serviços de Acessibilidade\" para ser executado, abra \"Configurações-> Serviços de Acessibilidade\" ou \"Configurações-> Serviços de Acessibilidade-> Mais Serviços Baixados\", encontre este aplicativo e ative os Serviços de Acessibilidade \n também pode ser definido posteriormente no menu suspenso.</string>
  <string name="explain_accessibility_permission2">O software precisa abrir \"Serviços de Acessibilidade\" para ser executado, abra \"Configurações->Serviços de Acessibilidade\" ou \"Configurações->Serviços de Acessibilidade->Mais Serviços Baixados\", encontre este aplicativo e ative os Serviços de Acessibilidade</string>
  <string name="text_cancel">Cancelar</string>
  <string name="text_path_is_empty">O caminho está vazio</string>
  <string name="text_file_not_exists">Arquivo não existe</string>
  <string name="text_no_file_rw_permission">Sem permissões de leitura e gravação de arquivos</string>
  <string name="text_no_accessibility_permission">Serviço de acessibilidade não iniciado</string>
  <string name="text_drawer_close">Feche o menu suspenso</string>
  <string name="text_drawer_open">Abra o menu suspenso</string>
  <string name="text_undo">Revogar</string>
  <string name="text_redo">Refazer</string>
  <string name="text_save">Salve </string>
  <string name="text_save_as_project">Salvar como projeto</string>
  <string name="text_run">Corre</string>
  <string name="text_alert">Dica</string>
  <string name="edit_exit_without_save_warn">O conteúdo não foi salvo. Tem certeza de que deseja sair?</string>
  <string name="text_select_save_mode">Selecione um método de salvamento</string>
  <string name="text_save_and_exit">Salvar e sair</string>
  <string name="text_save_as_project_and_exit">Salvar como projeto e sair</string>
  <string name="text_exit_directly">Sair diretamente</string>
  <string name="text_setting">Configurar</string>
  <string name="text_exit">Sair</string>
  <string name="text_auto_operate_service">Serviços de acessibilidade</string>
  <string name="text_about">Cerca de</string>
  <string name="text_licenses">Licença de código aberto</string>
  <string name="text_do_not_remind_again">Não lembre novamente</string>
  <string name="github">Código fonte do software</string>
  <string name="text_already_copy_to_clip">Copiado para a área de transferência</string>
  <string name="text_qq_already_copy_to_clip">O número do grupo QQ foi copiado para a área de transferência</string>
  <string name="share_app">[Autox.js] Endereço de download: https://github.com/kkevsekk1/AutoX/releases</string>
  <string name="text_floating_window">Janela flutuante</string>
  <string name="text_error_report">Relatório de erro</string>
  <string name="text_press_again_to_exit">Pressione novamente para sair do programa</string>
  <string name="text_already_stop_n_scripts">%d scripts em execução parados</string>
  <string name="text_start_running">Iniciar a operação</string>
  <string name="text_open_by_other_apps">Abrir com outro aplicativo</string>
  <string name="text_rename">Renomear</string>
  <string name="text_send_shortcut">Criar atalho</string>
  <string name="text_already_create">Criada</string>
  <string name="text_delete">Excluir</string>
  <string name="text_already_delete">Deletado</string>
  <string name="text_error">Erro</string>
  <string name="text_copy_debug_info">Copiar informações de depuração</string>
  <string name="text_it_is_the_developer_of_app">Este é um dos desenvolvedores de software(.・・)ノ</string>
  <string name="text_please_choose">Por favor escolha</string>
  <string name="text_crash">Recolhido o(≧口≦)o</string>
  <string name="crash_feedback">As mensagens de erro serão enviadas automaticamente. Você também pode copiar manualmente as informações de depuração e enviá-las ao desenvolvedor (*^▽^*) ou clicar em \"Sair\" para sair do programa(╥﹏╥)o</string>
  <string name="text_clear">Vazio</string>
  <string name="text_console">Console</string>
  <string name="text_log">Registro</string>
  <string name="text_report_fail">Falha no envio</string>
  <string name="text_report_succeed">Submetido com sucesso</string>
  <string name="edit_and_run_handle_intent_error">Não foi possível processar o arquivo</string>
  <string name="text_issue_report">Retorno</string>
  <string name="text_script_record">Gravar roteiro</string>
  <string name="text_start_record">Comece a gravar</string>
  <string name="text_recorded">Fim da gravação</string>
  <string name="text_copy_to_clip">Copiar para área de transferência</string>
  <string name="text_file_write_fail">Falha na gravação do arquivo</string>
  <string name="text_use_volume_control_record">Use a tecla de diminuir o volume para controlar</string>
  <string name="summary_use_volume_control_record">Depois de abrir a janela flutuante, cada vez que a tecla diminuir volume iniciará ou interromperá a gravação do script</string>
  <string name="text_qq_group">Este é o número do grupo QQ, se você não conseguir abri-lo, pesquise manualmente</string>
  <string name="text_mobile_qq_not_installed">QQ móvel não está instalado</string>
  <string name="text_edit">Editar</string>
  <string name="text_max_length_for_code_completion">Comprimento máximo do arquivo de conclusão de código</string>
  <string name="text_file_exists">O arquivo já existe</string>
  <string name="text_accessibility_service">Serviços de acessibilidade</string>
  <string name="text_enable_accessibility_service_by_root">Habilite automaticamente serviços com privilégios de root</string>
  <string name="text_auto_operate_service_enabled_but_not_running">O serviço de acessibilidade está ativado, mas não está em execução, pode ser um bug do Android, talvez seja necessário reiniciar o telefone ou reiniciar o serviço de acessibilidade</string>
  <string name="text_enable_accessibility_service_by_root_timeout">Tempo limite para iniciar o serviço de acessibilidade com privilégios de root</string>
  <string name="summary_enable_accessibility_service_by_root">Após a abertura, a execução do script abrirá automaticamente o serviço de acessibilidade com privilégios de root</string>
  <string name="text_appearance">Exterior</string>
  <string name="text_theme_color">Cor do tema</string>
  <string name="text_select_image">Selecione a imagem</string>
  <string name="text_help">Ajuda</string>
  <string name="text_no_floating_window_permission">Sem permissão de janela flutuante</string>
  <string name="text_no_accessibility_permission_to_capture">Serviço de acessibilidade não iniciado</string>
  <string name="text_record_msg">Prompt de gravação</string>
  <string name="text_text_changed">Mudança de texto</string>
  <string name="text_scrolled">Deslizar</string>
  <string name="text_long_clicked">Imprensa</string>
  <string name="text_clicked">Clique</string>
  <string name="text_copy_to_my_scripts">Importar para o meu script</string>
  <string name="text_import_succeed">Importação bem-sucedida</string>
  <string name="text_current_activity">Atividade atual:</string>
  <string name="text_current_package">Nome do pacote de aplicativos atual:</string>
  <string name="go_to_accessibility_settings">Por favor, abra Configurações->Serviços de Acessibilidade->Autox.js e habilite-o</string>
  <string name="text_script_running">Execução de script</string>
  <string name="text_use_volume_to_stop_running">A tecla de aumento de volume interrompe todos os scripts</string>
  <string name="text_need_to_enable_accessibility_service">Os serviços de acessibilidade precisam ser ativados</string>
  <string name="text_import_fail">Falha na importação</string>
  <string name="text_no_brower">Eu não tenho navegador o(╯□╰) então vá e instale um</string>
  <string name="text_refresh">Atualizar</string>
  <string name="notice_no_running_script">Nenhum script em execução φ(>ω&lt;*)</string>
  <string name="text_please_choose_a_script">Selecione um roteiro</string>
  <string name="text_edit_prepare_script">Editando scripts pré-executados</string>
  <string name="text_pre_execute_script">Script de pré-execução</string>
  <string name="text_clear_pre_execute_script">Limpar scripts pré-executados</string>
  <string name="text_clear_file_selection">Redefinir seleção de arquivo de script</string>
  <string name="summary_pre_execute_script">Será executado antes do arquivo de script ser executado</string>
  <string name="text_checking_update">Verificando atualizações</string>
  <string name="text_new_version">Nova versão</string>
  <string name="text_new_version2">Nova versão: %s</string>
  <string name="text_check_update_error">Falha na verificação de atualizações: %s</string>
  <string name="text_check_for_updates">Verifique se há atualizações</string>
  <string name="text_is_latest_version">Ja a ultima versao</string>
  <string name="text_version_too_old">A versão é muito baixa</string>
  <string name="warning_version_too_old">A versão atual é uma versão beta pública, com muitos problemas aparentes e potenciais, e não é mais adequada para uso contínuo.</string>
  <string name="text_update">Renovar</string>
  <string name="text_release_notes">Registro de alterações</string>
  <string name="text_download_url">Link para Download</string>
  <string name="text_directly_download">Download</string>
  <string name="text_download_failed">Download falhou</string>
  <string name="text_do_not_ask_again_for_this_version">Esta versão não solicita mais</string>
  <string name="text_code_beautify">Código de formato</string>
  <string name="text_processing">Em processamento</string>
  <string name="text_guard_mode">Modo protegido</string>
  <string name="summary_guard_mode">O comando de operação automática não pode ser executado nesta interface de software para evitar toques acidentais</string>
  <string name="text_layout_inspector_is_dumping">Análise de layout em andamento</string>
  <string name="text_force_stop">Parada forçada</string>
  <string name="text_execution_finished" formatted="false">\n ------------ \n [ %s ] Execução concluída em %f segundos</string>
  <string name="text_again">Novamente</string>
  <string name="text_again_and_again">Em dobro</string>
  <string name="text_again_and_again_again">Dobrar novamente</string>
  <string name="text_again_and_again_again_again">Dobrar novamente</string>
  <string name="text_connect_computer">Conectar computador</string>
  <string name="regist">Conecte-se à loja de aplicativos</string>
  <string name="text_server_address">Endereço do servidor</string>
  <string name="text_server_address_error">Endereço de link errado</string>
  <string name="text_annunciation">Declaração</string>
  <string name="text_about_me_and_repo">Sobre projetos e desenvolvedores</string>
  <string name="text_attribute">Atributos</string>
  <string name="text_value">Valor</string>
  <string name="text_show_widget_infomation">Ver informações de controle</string>
  <string name="text_show_layout_hierarchy">Visualizar na hierarquia de layout</string>
  <string name="text_show_layout_bounds">Visualizar no escopo do layout</string>
  <string name="text_more">Mais</string>
  <string name="text_script_save_successfully">Salvo com sucesso, atualize o diretório</string>
  <string name="text_run_repeatedly">Ciclo de corrida</string>
  <string name="text_loop_interval">Intervalo de ciclo (segundos)</string>
  <string name="text_loop_delay">Atraso na execução (segundos)</string>
  <string name="text_loop_times">Ciclos</string>
  <string name="hint_loop_times">0 é loop infinito</string>
  <string name="hint_loop_delay">Quantos segundos para atrasar para iniciar o loop</string>
  <string name="text_number_format_error">Formato incorreto</string>
  <string name="text_accessibility_settings">Serviços de acessibilidade aberta</string>
  <string name="text_enable_accessibitliy_service_by_root_failed">Falha ao abrir com privilégios de root</string>
  <string name="text_on_progress">Em processamento</string>
  <string name="text_delete_failed">Falha ao excluir</string>
  <string name="text_edit_script">Editar script</string>
  <string name="text_run_script">Executar script</string>
  <string name="text_import_script">Arquivo de script de importação</string>
  <string name="text_root_record_out_file_type">Tipo de arquivo gerado de gravação raiz</string>
  <string name="text_binary">Binário</string>
  <string name="summary_stable_mode">A análise de layout é mais estável quando habilitada, mas um pequeno número de scripts pode não funcionar corretamente. Reinicie o serviço de acessibilidade para entrar em vigor</string>
  <string name="text_hideLaucher">Ocultar ícones da área de trabalho (iniciar aplicativos via adb ou outros métodos)</string>
  <string name="text_stable_mode">Modo estável</string>
  <string name="text_single_build_clean_mode">Modo de atualização de empacotamento de arquivo único</string>
  <string name="summary_single_build_clean_mode">Após a abertura, o arquivo de configuração de empacotamento não será salvo quando o arquivo único for empacotado e todas as informações de configuração, como a imagem de inicialização e o ícone do aplicativo, não serão salvas.</string>
  <string name="text_directory">Pasta</string>
  <string name="text_file">Documento</string>
  <string name="text_tutorial">Tutorial</string>
  <string name="text_community">Comunidade</string>
  <string name="text_manage">Gerir</string>
  <string name="not_login">Não é necessário login para funções normais</string>
  <string name="text_volume_down_control">Controle de tecla de redução de volume</string>
  <string name="text_auto_backup">Script de backup automático</string>
  <string name="text_others">Outro</string>
  <string name="text_import">Importar</string>
  <string name="text_click_too_frequently">Sua operação é muito rápida ヽ(#`Д´)ﾉ</string>
  <string name="text_username">Nome de usuário</string>
  <string name="text_password">Senha</string>
  <string name="text_login">Conecte-se</string>
  <string name="text_forgot_password">Esqueceu a senha?</string>
  <string name="text_username_cannot_be_empty">O nome de usuário não pode estar vazio</string>
  <string name="text_password_cannot_be_empty">Senha não pode ficar em branco</string>
  <string name="text_time">Tempo</string>
  <string name="text_size">Tamanho</string>
  <string name="text_type">Modelo</string>
  <string name="text_editor_theme">Temas do editor</string>
  <string name="text_jump_to_line">Pular para a linha</string>
  <string name="text_info">Em formação</string>
  <string name="text_find_or_replace">Encontrar/substituir</string>
  <string name="text_copy_all">Copiar tudo</string>
  <string name="text_copy_line">Copiar linha</string>
  <string name="text_delete_line">Excluir linha</string>
  <string name="format_editor_info" formatted="false">Caracteres: %d \n : %d \n : %s</string>
  <string name="text_find">Achar</string>
  <string name="text_replace">Substituir</string>
  <string name="text_regex">Expressão regular</string>
  <string name="text_replace_all">Substitua tudo</string>
  <string name="text_find_next">Baixa</string>
  <string name="text_find_prev">Acima</string>
  <string name="hint_regex">A sintaxe regular é a mesma do JavaScript, você pode usar $ 1 ~ 9 em vez de correspondências capturadas</string>
  <string name="text_jump">Pular</string>
  <string name="text_jump_to_end">Ir para o final do arquivo</string>
  <string name="text_jump_to_start">Vá para o início do arquivo</string>
  <string name="text_jump_to_line_start">Ir para o início da linha</string>
  <string name="text_jump_to_line_end">Ir para o fim da linha</string>
  <string name="text_exit_floating_window">Sair da janela flutuante</string>
  <string name="text_select_save_path">Salvar em</string>
  <string name="text_cancel_download">Cancelar download</string>
  <string name="text_download">Download</string>
  <string name="format_file_downloaded" formatted="true">Arquivo salvo em %s</string>
  <string name="confirm_overwrite_file">O arquivo já existe, deseja substituí-lo?</string>
  <string name="text_select_file_to_upload">Selecione o arquivo para upload</string>
  <string name="text_select_file_to_import">Selecione o arquivo para importar</string>
  <string name="text_build_apk">Aplicativos em pacote</string>
  <string name="text_select">Escolher</string>
  <string name="text_output_apk_path">Local de salvamento do aplicativo empacotado</string>
  <string name="text_config">Configurar</string>
  <string name="text_app_name">Nome da Aplicação</string>
  <string name="text_package_name">Nomes de pacotes</string>
  <string name="text_version_name">Nome da versão</string>
  <string name="text_version_code">Número da versão</string>
  <string name="text_run_config">Executar configuração</string>
  <string name="text_hideLogs">Esconder registro</string>
  <string name="text_required_accessibility_service">Serviços de acessibilidade necessários</string>
  <string name="text_required_terminal_emulator">Emulador de terminal</string>
  <string name="text_required_background_start">Requer permissão de interface pop-up em segundo plano</string>
  <string name="text_required_draw_overlay">Permissão de janela flutuante necessária</string>
  <string name="text_volumeUpcontrol">Tecla de aumento de volume para finalizar a tarefa</string>
  <string name="text_splash_text">Texto da interface de inicialização</string>
  <string name="text_splash_icon">Iniciar ícone da interface</string>
  <string name="text_service_desc_text">Descrição dos Serviços de Acessibilidade</string>
  <string name="text_sign">Sinal</string>
  <string name="text_sign_choose">Selecionar assinatura</string>
  <string name="text_sign_password">Senha do certificado de assinatura</string>
  <string name="text_sign_password_input">Por favor, verifique a senha</string>
  <string name="text_sign_manage">Gerenciamento de assinatura</string>
  <string name="text_sign_key_add">Criar assinatura</string>
  <string name="text_sign_key_import">Importar assinatura</string>
  <string name="text_sign_hint_key_path">* Caminho para salvar arquivo</string>
  <string name="text_sign_hint_key_alias">*alias</string>
  <string name="text_sign_hint_key_password">*senha</string>
  <string name="text_sign_hint_key_year">*Período de validade (anos)</string>
  <string name="text_sign_key_year_def">25</string>
  <string name="text_sign_hint_key_country">*Código do país (XX)</string>
  <string name="text_sign_key_country_def">PT</string>
  <string name="text_sign_hint_not_must">Os seguintes são parâmetros opcionais</string>
  <string name="text_sign_hint_key_name">Nome</string>
  <string name="text_sign_hint_key_org">Organizar</string>
  <string name="text_sign_hint_key_org_unit">Unidade organizacional</string>
  <string name="text_sign_hint_key_province">Província</string>
  <string name="text_sign_hint_key_city">Cidade</string>
  <string name="text_sign_create_success">Criado com sucesso</string>
  <string name="text_sign_create_fail">Falha ao criar</string>
  <string name="text_build_successfully">Empacotado com sucesso</string>
  <string name="text_install">Instalar</string>
  <string name="text_build_failed">Falha no empacotamento, mensagem de erro:</string>
  <string name="format_build_successfully" formatted="true">Arquivo apk salvo em %s</string>
  <string name="apk_builder_prepare">Documento está sendo preparado</string>
  <string name="apk_builder_build">Prédio</string>
  <string name="apk_builder_package">Embalagem</string>
  <string name="apk_builder_sign">Assinatura</string>
  <string name="apk_builder_clean">Limpar arquivos temporários</string>
  <string name="text_source_file_path">Caminho do arquivo de script (pasta)</string>
  <string name="text_search">Procurar</string>
  <string name="text_select_icon">Selecione o ícone</string>
  <string name="text_use_android_n_shortcut">Adicionado ao atalho do Android 7.0</string>
  <string name="text_login_succeed">Login bem sucedido</string>
  <string name="text_login_fail">Falha na autenticação</string>
  <string name="text_reset_password">Redefinir senha</string>
  <string name="text_logining">Logando</string>
  <string name="text_register">Registro</string>
  <string name="text_email">Correspondência</string>
  <string name="text_email_cannot_be_empty">O e-mail não pode ficar vazio</string>
  <string name="text_email_format_error">Erro de formato de e-mail</string>
  <string name="text_registering">Registrando</string>
  <string name="text_register_fail">Registração falhou</string>
  <string name="text_register_succeed">Sucesso no registro</string>
  <string name="text_sample">Código de amostra</string>
  <string name="text_reset_to_initial_content">Redefinir para o conteúdo original</string>
  <string name="text_reset_succeed">Redefinir com sucesso</string>
  <string name="text_cannot_read_file">Não foi possível ler o arquivo</string>
  <string name="text_service">Servir</string>
  <string name="text_notification_permission">Notificar permissão de leitura</string>
  <string name="text_open_main_activity">Abra a interface principal</string>
  <string name="text_generate_code">Gerar código</string>
  <string name="text_action">Ação</string>
  <string name="text_click">Clique</string>
  <string name="text_long_click">Pressione longamente (longClick)</string>
  <string name="text_set_text">Definir texto (setText)</string>
  <string name="text_scroll_forward">Deslize para frente/direita/baixo (scrollForward)</string>
  <string name="text_scroll_backward">Rolar para trás/cima/esquerda (rolagem para trás)</string>
  <string name="text_find_one">Até que um seja encontrado (findOne)</string>
  <string name="text_until_find">Até que todos sejam encontrados (até encontrar)</string>
  <string name="text_wait_for">Aguarde o controle aparecer (waitFor)</string>
  <string name="text_selector_exists">Julgando a existência do controle (existe)</string>
  <string name="text_options">Opções</string>
  <string name="text_using_id_selector">Selecione usando id</string>
  <string name="text_using_text_selector">Usar seleção de texto (texto)</string>
  <string name="text_using_desc_selector">Selecione usando a descrição (desc)</string>
  <string name="text_generate">Gerar</string>
  <string name="text_generate_fail">Falha na construção o(╥﹏╥)o</string>
  <string name="text_documentation_source">Fonte de documentação</string>
  <string name="text_send">Mandar</string>
  <string name="text_timed_task">Tarefa cronometrada</string>
  <string name="text_running_task">Script em execução</string>
  <string name="text_done">Terminar</string>
  <string name="text_disposable_task">Corra apenas uma vez</string>
  <string name="text_daily_task">Correr diariamente</string>
  <string name="text_weekly_task">Executado por semana</string>
  <string name="text_timing">Cronometragem</string>
  <string name="text_disposable_task_time_before_now">O tempo da tarefa é menor que o tempo atual</string>
  <string name="text_next_run_time">Próxima corrida</string>
  <string name="text_day1">Na segunda-feira</string>
  <string name="text_day2">Terça-feira</string>
  <string name="text_day3">Quarta-feira</string>
  <string name="text_day4">Quinta-feira</string>
  <string name="text_day5">Sexta-feira</string>
  <string name="text_day6">Sábado</string>
  <string name="text_day7">Domingo</string>
  <string name="text_weekly_task_should_check_day_of_week">Selecione pelo menos um dia da semana</string>
  <string name="no_apk_builder_plugin">O plugin empacotado não está instalado, você gostaria de baixá-lo agora?</string>
  <string name="text_apk_builder_plugin_unavailable">Plugin de pacote não disponível</string>
  <string name="text_detail">Detalhes</string>
  <string name="text_open">Abrir</string>
  <string name="text_generated_code">Gerar resultados</string>
  <string name="text_copy">Cópia de</string>
  <string name="text_should_not_be_empty">Não pode estar vazio</string>
  <string name="text_icon">Ícone</string>
  <string name="text_device_not_rooted">O dispositivo não está enraizado</string>
  <string name="prompt_device_not_rooted">É detectado que seu dispositivo não está enraizado, o script de gravação requer privilégios de root, deseja continuar?</string>
  <string name="text_device_rooted">Ainda para gravar</string>
  <string name="text_community_category">Seção</string>
  <string name="text_community_unread">Não lida</string>
  <string name="text_community_recent">Atualizado</string>
  <string name="text_community_popular">Popular</string>
  <string name="text_community_tags">Tema</string>
  <string name="nodebb_error_invalid_login_credentials">Credenciais de login inválidas</string>
  <string name="nodebb_error_change_password_error_length">A senha é muito curta</string>
  <string name="nodebb_error_email_taken">Caixa de correio está ocupada</string>
  <string name="nodebb_error_change_password_error_match">As duas senhas inseridas não correspondem</string>
  <string name="text_logout">Sair</string>
  <string name="nodebb_error_forbidden">Falha na operação, tente novamente mais tarde</string>
  <string name="description_stable_mode">O modo estável torna o script mais estável ao capturar o layout omitindo os detalhes do layout, mas ao mesmo tempo buscando menos controles na tela. \n \n o serviço de acessibilidade para entrar em vigor.</string>
  <string name="text_size_min_value">2</string>
  <string name="text_size_max_value">60</string>
  <string name="text_text_size">Tamanho da fonte</string>
  <string name="text_preview">Visualizar</string>
  <string name="text_size_current_value" formatted="true">Tamanho da fonte: %d</string>
  <string name="error_regex_find_prev">A expressão regular não pode olhar para frente</string>
  <string name="text_notification">Perceber</string>
  <string name="apk_builder_plugin_version_too_low">A versão do plug-in empacotado é muito baixa e o aplicativo empacotado pode não ser executado corretamente. Deseja baixar a versão correspondente do plug-in empacotado agora?</string>
  <string name="text_inspect_layout_bounds">Análise de intervalo de layout</string>
  <string name="text_inspect_layout_hierarchy">Hierarquia de layout</string>
  <string name="text_inspect_layout">Análise de layout</string>
  <string name="text_pointer_location">Posição do ponteiro [Raiz]</string>
  <string name="text_change_script_dir">Alterar o caminho da pasta do script</string>
  <string name="text_error_copy_file" formatted="true">Ocorreu um erro: %s</string>
  <string name="text_debug">Depuração</string>
  <string name="text_set_breakpoint">Ponto de interrupção</string>
  <string name="text_launch_debugger">Iniciar a depuração</string>
  <string name="text_debug_step_out">Pular fora</string>
  <string name="text_debug_step_into">Digitar</string>
  <string name="text_debug_step_over">Único passo</string>
  <string name="text_debug_resume_script">Prosseguir</string>
  <string name="text_stop">Pare</string>
  <string name="text_new_watching_variable">Adicione uma variável ou expressão para observar</string>
  <string name="text_variable_or_expr">Variável ou expressão</string>
  <string name="text_remove_all_breakpoints">Remover todos os pontos de interrupção</string>
  <string name="hint_long_click_run_to_debug">A depuração também pode ser iniciada pressionando longamente o ícone \"Executar\"</string>
  <string name="format_debug_bar_title">Depurar [ %s ]</string>
  <string name="text_copy_value">Valor de cópia</string>
  <string name="text_execute_code">Executar código</string>
  <string name="text_code">Código</string>
  <string name="text_result">Resultado</string>
  <string name="text_close">Fecho</string>
  <string name="text_execute">Implemento</string>
  <string name="error_connect_to_remote">Falha na conexão: %s</string>
  <string name="text_are_you_sure_to_delete">Tem certeza de que deseja excluir %s</string>
  <string name="text_run_on_broadcast">Um evento específico (transmissão) aciona a operação</string>
  <string name="text_search_java_class">Procurar pacotes/classes Java</string>
  <string name="text_class_or_package_name">Nome da classe/pacote</string>
  <string name="text_view_docs">Ver documentação</string>
  <string name="text_market">Mercado</string>
  <string name="text_launch_config">Executar configuração</string>
  <string name="text_main_file_name">Nome do script principal</string>
  <string name="text_project">Projeto</string>
  <string name="text_project_location">Localização do projeto</string>
  <string name="text_new_project">Novo projeto</string>
  <string name="text_js_file">Arquivo js</string>
  <string name="text_invalid_project">Item inválido</string>
  <string name="text_invalid_project_config">Nenhum arquivo de configuração</string>
  <string name="text_foreground_service">Recepção</string>
  <string name="foreground_notification_channel_name">Notificação de serviço de recepção</string>
  <string name="foreground_notification_title">Autox.js continua em execução</string>
  <string name="foreground_notification_text">Clique para entrar na interface principal</string>
  <string name="text_run_on_other_broadcast">Outros eventos (transmissão)</string>
  <string name="text_broadcast_action">Ação de transmissão</string>
  <string name="text_run_on_boot">Ao inicializar</string>
  <string name="text_run_on_screen_on">Quando a tela está ligada</string>
  <string name="text_run_on_screen_off">Quando a tela está desligada</string>
  <string name="error_empty_selection">Selecione uma opção de evento de transmissão</string>
  <string name="text_run_on_battery_change">Quando o poder muda</string>
  <string name="text_run_on_screen_unlock">Quando a tela é desbloqueada</string>
  <string name="text_run_on_power_connect">Quando a energia está conectada</string>
  <string name="text_run_on_power_disconnect">Quando a energia é desconectada</string>
  <string name="text_run_on_conn_change">Quando a conexão de rede muda</string>
  <string name="text_run_on_package_install">Quando um novo aplicativo é instalado</string>
  <string name="text_run_on_package_uninstall">Quando o aplicativo é desinstalado</string>
  <string name="text_run_on_package_update">Ao aplicar uma atualização</string>
  <string name="text_run_on_headset_plug">Ao conectar e desconectar o fone de ouvido</string>
  <string name="text_run_on_time_tick">Todo minuto</string>
  <string name="text_run_on_config_change">Quando certas configurações (orientação da tela, região, etc.)</string>
  <string name="error_pattern_syntax">Erro de expressão regular</string>
  <string name="text_invalid_package_name">Nome do pacote ilegal</string>
  <string name="error_cannot_rename">Falha ao renomear</string>
  <string name="text_project_save_success">O projeto foi salvo em %s</string>
  <string name="text_project_save_error">Falha ao salvar projeto %s</string>
  <string name="text_night_mode">Modo noturno</string>
  <string name="text_run_on_startup">Quando o Autox.js é iniciado</string>
  <string name="text_usage_stats_permission">Acesso de uso</string>
  <string name="description_usage_stats_permission">Através da permissão \"ver estatísticas de uso\", você pode obter o uso anterior do aplicativo deste dispositivo, para que seja mais preciso obter o aplicativo atual (currentPackage).</string>
  <string name="text_open_with">Caminho aberto</string>
  <string name="text_root">Raiz</string>
  <string name="text_no_root">Livre de raiz</string>
  <string name="text_star">Coletar</string>
  <string name="text_upvote">Incrível</string>
  <string name="text_downvote">Pisar em</string>
  <string name="textSizePlus">+</string>
  <string name="textSizeMinus">-</string>
  <plurals name="air_error_short_description" key="air_error_short_description"> 
    <item quantity="one">A descrição deve ter pelo menos %d caracteres.</item>  
    <item quantity="other">A descrição deve ter pelo menos %d caracteres.</item> 
  </plurals>
  <string name="air_title_report_issue">Relatório de erro</string>
  <string name="air_title_issue">Pergunta</string>
  <string name="air_label_issue_title">Título</string>
  <string name="air_label_issue_description">Descrever</string>
  <string name="air_label_device_info">Informação de dispositivo</string>
  <string name="air_label_use_email">Enviar por email</string>
  <string name="air_label_use_guest">Enviar anonimamente</string>
  <string name="air_label_email">Correspondência</string>
  <string name="air_label_email_optional">Email de contato (opcional)</string>
  <string name="air_error_no_title">Por favor, insira uma pergunta</string>
  <string name="air_error_no_description">Insira uma descrição detalhada</string>
  <string name="air_error_no_username">Insira um nome de usuário válido do Github</string>
  <string name="air_error_no_password">Por favor, digite a senha correta</string>
  <string name="air_error_no_email">Por favor insira seu e-mail</string>
  <string name="air_dialog_title_loading">Carregando para o GitHub…</string>
  <string name="air_dialog_title_failed">Falha ao enviar relatório de bug</string>
  <string name="air_dialog_description_failed_wrong_credentials">Nome de usuário ou senha errado</string>
  <string name="air_dialog_description_failed_invalid_token">Token de acesso inválido. Entre em contato com o desenvolvedor do software.</string>
  <string name="air_dialog_description_failed_issues_not_available">Os problemas não estão disponíveis, entre em contato com o desenvolvedor do software.</string>
  <string name="air_dialog_description_failed_unknown">Erro desconhecido</string>
  <string name="air_dialog_action_failed">OK</string>
  <string name="text_template_apk_not_found">O modelo apk não pode ser encontrado, copie o arquivo apk no diretório \"build/outputs/apk/common/release\" para \"app/src/main/assets\" e nomeie-o como \"template.apk\" e reconstrua-o</string>
  <string name="desc_back">De volta</string>
  <string name="desc_add_icon">Adicionar ícone</string>
  <string name="text_default_signature">Assinatura padrão</string>
  <string name="desc_done">Terminar</string>
  <string name="apk_icon">Ícone do APK</string>
  <string name="text_packaging_options">Opções de compilação</string>
  <string name="text_verification_failed">Falha na verificação</string>
  <string name="text_WebX">WebX</string>
  <string name="text_file_manager_permission">Todos os acessos a arquivos</string>
  <string name="text_fullscreen">Alternar o modo de tela cheia 1</string>
  <string name="text_drawer_right">Abra o menu direito</string>
  <string name="text_required_default_paddle_ocr_model">Dados de treinamento padrão do PaddleOCR do pacote</string>
  <string name="switch_fullscreen">Alternar o modo de tela cheia</string>
  <string name="text_custom_ocr_model_path">Diretório de modelo de OCR personalizado do pacote</string>
  <string name="desc_more">Mais</string>
  <string name="text_switch_web_kernel">Mudar o kernel da web</string>
  <string name="text_favorites_management">Gerenciamento de coleção</string>
  <string name="text_switch_timed_task_scheduler">Alternar agendador de tarefas cronometradas</string>
  <string name="text_go_to_open">Abrir</string>
  <string name="text_home">Casa</string>
  <string name="text_management">Gerir</string>
  <string name="text_document">Documentação</string>
  <string name="text_open_usb_debug">Ativar a depuração USB</string>
  <string name="text_scan_qr">Código de digitalização</string>
  <string name="text_connect_failed">Falha na conexão: %s , verifique se o firewall do computador permite a porta 9317, se está na mesma rede local e se o plug-in VS CODE abriu o serviço...</string>
  <string name="text_start_service_failed">Falha ao iniciar o serviço: %s</string>
  <string name="text_unsupported_qr_code">Os códigos QR neste formato não são suportados</string>
  <string name="text_connected_to_computer">Conectado ao computador</string>
  <string name="text_press_again_to_exit_app">Pressione novamente para sair do aplicativo</string>
  <string name="text_app_download_link">Link para Download</string>
  <string name="text_project_link">Endereço do projeto</string>
  <string name="text_release_date">Publicado: %s</string>
  <string name="text_checking_for_updates">Verificando atualizações</string>
  <string name="text_update_information_not_found">Nenhuma informação de atualização encontrada</string>
  <string name="text_please_enable_external_storage">Ative a permissão de armazenamento externo primeiro</string>
  <string name="text_app_detail_settings">Configurações de detalhes do aplicativo</string>
  <string name="text_logcat">Registro</string>
  <string name="text_menu">Cardápio</string>
  <string name="text_exit_search">Sair da pesquisa</string>
  <string name="text_licenses_other">Licença de código não aberto</string>
  <string name="text_save_successfully">Salvo com sucesso</string>
  <string name="text_invalid_config">Configuração inválida</string>
  <string name="text_abi">ABIs compatíveis (separadas por vírgulas)</string>
  <string name="text_save_project_to">Projeto salvo em: %s</string>
  <string name="text_hide_and_show">Ocultar/mostrar</string>
  <string name="text_browser_open">Navegador aberto</string>
  <string name="text_do_nothing">Fazer nada</string>
  <string name="text_copy_to_new_folder">Copie o conteúdo da pasta original para a nova pasta</string>
  <string name="text_move_new_folder">Mova o conteúdo da pasta original para a nova pasta</string>
  <string name="text_accessibility_service_is_not_enable">O serviço de acessibilidade não está ativado</string>
  <string name="text_set_successfully">Definido com sucesso</string>
  <string name="text_reconnecting">Reconectando</string>
  <string name="text_connecting">Conectando</string>
  <string name="text_handshake_failed">Aperto de mão falhou</string>
</resources>
