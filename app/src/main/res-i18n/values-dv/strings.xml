<?xml version="1.0" encoding="UTF-8"?>

<!--Automatically translated by Globalization Translator (https://github.com/wilinz/globalization-translator)-->
<resources>
  <string name="text_accessibility_service_description">ސްކްރިޕްޓް އޮޕަރޭޝަންތައް އޮޓޮމެޓިކް ކުރުމަށް ބޭނުންވާ (ކްލިކް، ލޯންގް ޕްރެސް، ސްވައިޕް ފަދަ)، ޑިސެބިލް ކޮށްފައިވާނަމަ، އެގްޒެކެޓް ކުރެވޭނީ ހަމައެކަނި އޮޓޮމެޓިކް އޮޕަރޭޝަންތައް ނުހިމެނޭ ސްކްރިޕްޓްތަކެވެ.</string>
  <string name="text_new_file">އާ ފައިލްއެއް އުފައްދާށެވެ</string>
  <string name="text_create_fail">އުފެއްދުމަށް ނާކާމިޔާބުވި</string>
  <string name="text_please_input_name">ނަމެއް ޖައްސަވާށެވެ</string>
  <string name="text_name">ނަން</string>
  <string name="text_go_to_setting">ސެޓިންގސް އަށް ދާށެވެ</string>
  <string name="explain_accessibility_permission">ސޮފްޓްވެއާ ހިންގުމަށްޓަކައި \"އެކްސެސިބިލިޓީ ސާވިސަސް\" ހުޅުވަން ޖެހޭނެ، \"ސެޓިންގސް->އެކްސެސިބިލިޓީ ސާވިސަސް\" ނުވަތަ \"ސެޓިންގސް->އެކްސެސިބިލިޓީ ސާވިސަސް->އިތުރަށް ޑައުންލޯޑް ކުރެވިފައިވާ ސާވިސަސް\" ހުޅުވައި، މި އެޕްލިކޭޝަން ހޯދައި، އެކްސެސިބިލިޓީ ސާވިސަސް \n ތިބާ އެނެބަލް ކުރަން ޖެހޭނެ ފަހުން ޑްރޮޕް ޑައުން މެނޫއިން ފެންނާނެއެވެ.</string>
  <string name="explain_accessibility_permission2">ސޮފްޓްވެއާ ހިންގުމަށްޓަކައި \"އެކްސެސިބިލިޓީ ސާވިސަސް\" ހުޅުވަން ޖެހޭއިރު، \"ސެޓިންގސް->އެކްސެސިބިލިޓީ ސާވިސަސް\" ނުވަތަ \"ސެޓިންގސް->އެކްސެސިބިލިޓީ ސާވިސަސް->މޯރ ޑައުންލޯޑްޑް ސާވިސަސް\" ހުޅުވައި، މި އެޕްލިކޭޝަން ހޯދައި، އެކްސެސިބިލިޓީ ސާވިސަސް އެނެބަލް ކުރައްވާށެވެ</string>
  <string name="text_cancel">ކެންސަލް</string>
  <string name="text_path_is_empty">މަގު ހުސްވެއްޖެއެވެ</string>
  <string name="text_file_not_exists">ފައިލް އެއް ނެތެވެ</string>
  <string name="text_no_file_rw_permission">ފައިލް ކިޔުމާއި ލިޔުމުގެ ހުއްދައެއް ނެތެވެ</string>
  <string name="text_no_accessibility_permission">އެކްސެސިބިލިޓީގެ ހިދުމަތް ނުފެށި</string>
  <string name="text_drawer_close">ޑްރޮޕް ޑައުން މެނޫ ބަންދުކޮށްލާށެވެ</string>
  <string name="text_drawer_open">ޑްރޮޕް ޑައުން މެނޫ ހުޅުވާލާށެވެ</string>
  <string name="text_undo">ބާތިލް ކުރުން</string>
  <string name="text_redo">އަލުން ކުރާށެވެ</string>
  <string name="text_save">ރައްކާކުރުން</string>
  <string name="text_save_as_project">ޕްރޮޖެކްޓެއްގެ ގޮތުގައި ސޭވް ކުރާށެވެ</string>
  <string name="text_run">ދުވުން</string>
  <string name="text_alert">އިޝާރާތެއް</string>
  <string name="edit_exit_without_save_warn">ކޮންޓެންޓް ސޭވްކޮށްފައި ނުވާތީ، އެގްޒިޓް ކުރަން ބޭނުންވާކަން ޔަގީން ހެއްޔެވެ؟</string>
  <string name="text_select_save_mode">ސޭވް މެތޯޑެއް ހޮވާށެވެ</string>
  <string name="text_save_and_exit">ސޭވްކޮށް އެގްޒިޓް ކުރާށެވެ</string>
  <string name="text_save_as_project_and_exit">ޕްރޮޖެކްޓްގެ ގޮތުގައި ސޭވްކޮށް އެގްޒިޓް ކުރާށެވެ</string>
  <string name="text_exit_directly">ސީދާ ނުކުމެލާށެވެ</string>
  <string name="text_setting">ސެޓް އަޕް</string>
  <string name="text_exit">ދޫކޮށްލުން</string>
  <string name="text_auto_operate_service">އެކްސެސިބިލިޓީ ސާވިސަސް</string>
  <string name="text_about">ގުޅިގެން</string>
  <string name="text_licenses">އޯޕަން ސޯސް ލައިސަންސް</string>
  <string name="text_do_not_remind_again">އަނެއްކާ ހަނދާންކޮށްނުލާށެވެ</string>
  <string name="github">ސޮފްޓްވެއަރ ސޯސް ކޯޑް</string>
  <string name="text_already_copy_to_clip">ކްލިޕްބޯޑަށް ކޮޕީކޮށްފައި</string>
  <string name="text_qq_already_copy_to_clip">ކިއުކިޔު ގްރޫޕް ނަންބަރު ކްލިޕްބޯޑަށް ކޮޕީކޮށްފައިވެއެވެ</string>
  <string name="share_app">[Autox.js] ޑައުންލޯޑް އެޑްރެސް: https://github.com/kkevsekk1/AutoX/releases</string>
  <string name="text_floating_window">ފްލޯޓިންގ ވިންޑޯ</string>
  <string name="text_error_report">އެރަރ ރިޕޯޓް</string>
  <string name="text_press_again_to_exit">ޕްރޮގްރާމް އިން ނުކުތުމަށް އަނެއްކާވެސް ފިއްތާލާށެވެ</string>
  <string name="text_already_stop_n_scripts">%d ސްކްރިޕްޓްތައް ހިންގުން ހުއްޓިއްޖެއެވެ</string>
  <string name="text_start_running">އޮޕަރޭޝަން ފެށުން</string>
  <string name="text_open_by_other_apps">އެހެން އެޕަކުން ހުޅުވާލާށެވެ</string>
  <string name="text_rename">ނަން ބަދަލުކުރުން</string>
  <string name="text_send_shortcut">ޝޯޓްކަޓް އުފެއްދުން</string>
  <string name="text_already_create">އުފެއްދެވި އެވެ</string>
  <string name="text_delete">ޑިލީޓް</string>
  <string name="text_already_delete">ޑިލީޓް ކޮށްފިއެވެ</string>
  <string name="text_error">ކުށް</string>
  <string name="text_copy_debug_info">ޑީބަގް އިންފޯ ކޮޕީ ކުރާށެވެ</string>
  <string name="text_it_is_the_developer_of_app">މިއީ އެއް ސޮފްޓްވެއާ ޑިވެލޮޕަރ(.・・)ノ އެވެ</string>
  <string name="text_please_choose">ޕްލީޒް ޗޮއިސް</string>
  <string name="text_crash">ކޮލަޕްސްޑް o(≧口≦)o</string>
  <string name="crash_feedback">އެރަރ މެސެޖުތައް އޮޓޮމެޓިކުން ހުށަހަޅާނެއެވެ. އަދި ޑިބަގިންގ މަޢުލޫމާތު މެނުއަލްކޮށް ކޮޕީކޮށް ޑިވެލޮޕަރަށް ހުށަހެޅޭނެ (*^▽^*) ނުވަތަ \"އެގްޒިޓް\" އަށް ފިތާލުމުން programo(╥ṏ╥)o އިން ނުކުމެވޭނެއެވެ</string>
  <string name="text_clear">ހުސްވެފަ</string>
  <string name="text_console">ކޮންސޯލް އެވެ</string>
  <string name="text_log">ވަކަރު ބުރި</string>
  <string name="text_report_fail">ހުށަހެޅުން ނާކާމިޔާބުވެއްޖެއެވެ</string>
  <string name="text_report_succeed">ކާމިޔާބުކަމާއެކު ހުށަހެޅިއްޖެއެވެ</string>
  <string name="edit_and_run_handle_intent_error">ފައިލް ޕްރޮސެސް ނުކުރެވުނެވެ</string>
  <string name="text_issue_report">ފީޑްބެކް</string>
  <string name="text_script_record">ރެކޯޑް ސްކްރިޕްޓް</string>
  <string name="text_start_record">ރެކޯޑް ކުރަން ފަށާށެވެ</string>
  <string name="text_recorded">ރެކޯޑިންގ ނިމުމުން</string>
  <string name="text_copy_to_clip">ކްލިޕްބޯޑަށް ކޮޕީ ކުރާށެވެ</string>
  <string name="text_file_write_fail">ފައިލް ލިޔުން ފެއިލްވެއްޖެއެވެ</string>
  <string name="text_use_volume_control_record">ކޮންޓްރޯލް ކުރުމަށް ވޮލިއުމް ޑައުން ކީ ބޭނުން ކުރާށެވެ</string>
  <string name="summary_use_volume_control_record">ފްލޯޓިންގ ވިންޑޯ ހުޅުވުމަށްފަހު ކޮންމެ ފަހަރަކު ވޮލިއުމް ޑައުން ކީ އިން ސްކްރިޕްޓް ރެކޯޑިންގ ފެށޭނެ ނުވަތަ ހުއްޓޭނެއެވެ</string>
  <string name="text_qq_group">މިއީ ކިއުކިޔު ގްރޫޕް ނަންބަރު، ހުޅުވޭގޮތް ނުވެއްޖެނަމަ މެނުއަލްކޮށް ސަރޗް ކޮށްލައްވާށެވެ</string>
  <string name="text_mobile_qq_not_installed">މޯބައިލް ކިއުކިއު އިންސްޓޯލް ނުކުރެ އެވެ</string>
  <string name="text_edit">ބަދަލު ގެނައުން</string>
  <string name="text_max_length_for_code_completion">ކޯޑް ފުރިހަމަ ކުރެވުނު އެންމެ ގިނަ ފައިލް ދިގުމިން</string>
  <string name="text_file_exists">ފައިލް މިހާރުވެސް އެބައޮތެވެ</string>
  <string name="text_accessibility_service">އެކްސެސިބިލިޓީ ސާވިސަސް</string>
  <string name="text_enable_accessibility_service_by_root">ރޫޓް އިމްތިޔާޒުތަކާއެކު ޚިދުމަތްތައް އޮޓޮމެޓިކުން އެނެބަލްކުރުން</string>
  <string name="text_auto_operate_service_enabled_but_not_running">އެކްސެސިބިލިޓީ ސާވިސް އެނެބަލް ކޮށްފައި އޮތް ނަމަވެސް ނުހިނގާ، މިއީ އެންޑްރޮއިޑް ބަގެއް ކަމަށް ވެދާނެ، ފޯނު އަލުން ސްޓާޓް ކުރަން ނުވަތަ އެކްސެސިބިލިޓީ ސާވިސް އަލުން ސްޓާޓް ކުރަން ޖެހިދާނެ އެވެ</string>
  <string name="text_enable_accessibility_service_by_root_timeout">ރޫޓް އިމްތިޔާޒުތަކާއެކު އެކްސެސިބިލިޓީ ޚިދުމަތް ފެށުމަށް ޓައިމްއައުޓް</string>
  <string name="summary_enable_accessibility_service_by_root">ހުޅުވުމަށްފަހު ސްކްރިޕްޓް ހިންގުމުން އޮޓޮމެޓިކުން ރޫޓް ޕްރިވިލެޖްތަކާއެކު އެކްސެސިބިލިޓީ ސާވިސް ހުޅުވޭނެއެވެ</string>
  <string name="text_appearance">ބޭރު ފަރާތް</string>
  <string name="text_theme_color">ތީމް ކުލައެވެ</string>
  <string name="text_select_image">އިމޭޖް ހޮވާށެވެ</string>
  <string name="text_help">އެހީވުން</string>
  <string name="text_no_floating_window_permission">ފްލޯޓިންގ ވިންޑޯގެ ހުއްދައެއް ނެތެވެ</string>
  <string name="text_no_accessibility_permission_to_capture">އެކްސެސިބިލިޓީގެ ހިދުމަތް ނުފެށި</string>
  <string name="text_record_msg">ރެކޯޑިންގ ޕްރޮމްޕްޓް</string>
  <string name="text_text_changed">ޓެކްސްޓް ބަދަލުކުރުން</string>
  <string name="text_scrolled">ސްލައިޑް</string>
  <string name="text_long_clicked">ނޫސްވެރިން</string>
  <string name="text_clicked">ކްލިކް ކުރާށެވެ</string>
  <string name="text_copy_to_my_scripts">އަހަރެންގެ ސްކްރިޕްޓަށް އިމްޕޯޓް ކުރާށެވެ</string>
  <string name="text_import_succeed">އިމްޕޯޓް ކާމިޔާބު ވެއްޖެ</string>
  <string name="text_current_activity">މިހާރު ހިނގަމުންދާ ހަރަކާތްތައް:</string>
  <string name="text_current_package">މިހާރު ބޭނުންކުރާ އެޕްލިކޭޝަން ޕެކޭޖް ނަން:</string>
  <string name="go_to_accessibility_settings">ސެޓިންގސް->އެކްސެސިބިލިޓީ ސާވިސަސް->Autox.js ހުޅުވާލުމަށްފަހު އެނެބަލް ކުރައްވާށެވެ</string>
  <string name="text_script_running">ސްކްރިޕްޓް ރަން</string>
  <string name="text_use_volume_to_stop_running">ވޮލިއުމް އަޕް ކީ އިން ހުރިހާ ސްކްރިޕްޓެއް ހުއްޓިގެންދެއެވެ</string>
  <string name="text_need_to_enable_accessibility_service">އެކްސެސިބިލިޓީގެ ހިދުމަތްތައް އެނެބަލް ކުރަން ޖެހެ އެވެ</string>
  <string name="text_import_fail">އިމްޕޯޓް ނާކާމިޔާބުވެއްޖެ</string>
  <string name="text_no_brower">އަހަރެންގެ އަތުގައި ބްރައުޒާއެއް ނެތް o(╯□╰)o ގޮސް އިންސްޓޯލް ކޮށްލާށެވެ</string>
  <string name="text_refresh">ރިފްރެޝް ކޮށްލާށެވެ</string>
  <string name="notice_no_running_script">ދުއްވާ ސްކްރިޕްޓެއް ނެތް φ(>ω&lt;*)</string>
  <string name="text_please_choose_a_script">ސްކްރިޕްޓެއް ހޮވާށެވެ</string>
  <string name="text_edit_prepare_script">ކުރިން އެގްޒެކެޓް ކުރެވިފައިވާ ސްކްރިޕްޓްތައް އެޑިޓްކުރުން</string>
  <string name="text_pre_execute_script">ޕްރީ އެގްޒެކެޓް ސްކްރިޕްޓް</string>
  <string name="text_clear_pre_execute_script">ކުރިން އެގްޒެކެޓް ކުރެވިފައިވާ ސްކްރިޕްޓްތައް ސާފުކުރުން</string>
  <string name="text_clear_file_selection">ސްކްރިޕްޓް ފައިލް ހޮވުން ރީސެޓް ކުރުން</string>
  <string name="summary_pre_execute_script">ސްކްރިޕްޓް ފައިލް އެގްޒެކެޓް ކުރުމުގެ ކުރިން އެގްޒެކެޓް ކުރެވޭނެއެވެ</string>
  <string name="text_checking_update">އަޕްޑޭޓްތައް ހުރިތޯ ބެލުން</string>
  <string name="text_new_version">އައު ވަރޝަން</string>
  <string name="text_new_version2">އައު ވަރޝަން: %s</string>
  <string name="text_check_update_error">އަޕްޑޭޓްތައް ފެއިލްވިތޯ ބަލާށެވެ: %s</string>
  <string name="text_check_for_updates">އަޕްޑޭޓްތައް ހުރިތޯ ބަލާށެވެ</string>
  <string name="text_is_latest_version">މިހާރުވެސް އެންމެ ފަހުގެ ވަރޝަން</string>
  <string name="text_version_too_old">ވަރޝަން މާ ދަށް</string>
  <string name="warning_version_too_old">މިހާރު ބޭނުންކުރާ ވަރޝަނަކީ ޕަބްލިކް ބީޓާ ވަރޝަނެއް ކަމަށާއި، ފެންނަ ފެނުމުގައި އަދި ކުރިމަތިވެދާނެ ގިނަ މައްސަލަތަކެއް ހުރި ވަރޝަނެއް ކަމަށާއި، މިއީ ކުރިއަށް އޮތްތަނުގައި ބޭނުން ކުރުމަށް އެކަށީގެންވާ އެއްޗެއް ނޫން ކަމަށެވެ.</string>
  <string name="text_update">އާކުރުން</string>
  <string name="text_release_notes">ޗޭންޖްލޮގް</string>
  <string name="text_download_url">ޑައުންލޯޑް ލިންކް</string>
  <string name="text_directly_download">ޑައުންލޯޑް</string>
  <string name="text_download_failed">ޑައުންލޯޑް ފެއިލްވެއްޖެއެވެ</string>
  <string name="text_do_not_ask_again_for_this_version">މި ވަރޝަން އިން ދެން ޕްރޮމްޕްޓް ނުކުރެއެވެ</string>
  <string name="text_code_beautify">ފޯމެޓް ކޯޑެވެ</string>
  <string name="text_processing">ޕްރޮސެސިންގ</string>
  <string name="text_guard_mode">ޕްރޮޓެކްޓެޑް މޯޑެވެ</string>
  <string name="summary_guard_mode">މި ސޮފްޓްވެއާ އިންޓަފޭސްގައި އޮޓޮމެޓިކް އޮޕަރޭޝަން ކޮމާންޑް ނުހިންގޭނީ އެކްސިޑެންޓް ވެގެން އަތްލުމުން ސަލާމަތްވުމަށްޓަކައެވެ</string>
  <string name="text_layout_inspector_is_dumping">ލޭއައުޓް ތަޙުލީލު ކުރުމުގެ މަސައްކަތް ކުރިއަށް އެބަދެއެވެ</string>
  <string name="text_force_stop">މަޖުބޫރުކޮށްގެން ހުއްޓާލާށެވެ</string>
  <string name="text_execution_finished" formatted="false">\n ------------ \n [ %s ] %f ސިކުންތުގައި ހިންގުން ނިމިއްޖެ</string>
  <string name="text_again">އަނެއްކާ</string>
  <string name="text_again_and_again">ދެފަަހަރު</string>
  <string name="text_again_and_again_again">އަނެއްކާވެސް ޑަބަލް ކޮށްލާށެވެ</string>
  <string name="text_again_and_again_again_again">އަނެއްކާވެސް ޑަބަލް ކޮށްލާށެވެ</string>
  <string name="text_connect_computer">ކޮމްޕިއުޓަރ ގުޅުވާށެވެ</string>
  <string name="regist">އެޕް ސްޓޯރާ ގުޅާލާށެވެ</string>
  <string name="text_server_address">ސަރވަރ އެޑްރެސް</string>
  <string name="text_server_address_error">ގޯސް ލިންކް އެޑްރެހެއް</string>
  <string name="text_annunciation">ބަޔާން</string>
  <string name="text_about_me_and_repo">ޕްރޮޖެކްޓްތަކާއި ޑިވެލޮޕަރުންނާ ބެހޭގޮތުން</string>
  <string name="text_attribute">އެޓްރިބިއުޓްސް</string>
  <string name="text_value">އަގު</string>
  <string name="text_show_widget_infomation">ކޮންޓްރޯލް މަޢުލޫމާތު ބަލާލުން</string>
  <string name="text_show_layout_hierarchy">ލޭއައުޓް ހައިރާކީގައި ބަލާލުން</string>
  <string name="text_show_layout_bounds">ލޭއައުޓް ސްކޯޕްގައި ބަލާލުން</string>
  <string name="text_more">އިތުރަށް</string>
  <string name="text_script_save_successfully">ކާމިޔާބުކަމާއެކު ސޭވް ކުރެވިއްޖެނަމަ، ޑައިރެކްޓަރީ ރިފްރެޝް ކުރައްވާށެވެ</string>
  <string name="text_run_repeatedly">ސައިކަލް ދުވުން</string>
  <string name="text_loop_interval">ސައިކަލް އިންޓަވަލް (ސިކުންތު) .</string>
  <string name="text_loop_delay">އެގްޒެކެޝަން ލަސްވުން (ސިކުންތު)</string>
  <string name="text_loop_times">ސައިކަލްތަކެވެ</string>
  <string name="hint_loop_times">0 އަކީ އިންފިނިޓް ލޫޕް އެވެ</string>
  <string name="hint_loop_delay">ލޫޕް ކުރަން ފެށުމަށް ކިތައް ސިކުންތު ލަސްކުރަންވީ ހެއްޔެވެ</string>
  <string name="text_number_format_error">ގޯސް ފޯމެޓެއް</string>
  <string name="text_accessibility_settings">އޯޕަން އެކްސެސިބިލިޓީ ސާވިސަސް</string>
  <string name="text_enable_accessibitliy_service_by_root_failed">ރޫޓް އިމްތިޔާޒުތަކާއެކު ހުޅުވުމަށް ނާކާމިޔާބުވިއެވެ</string>
  <string name="text_on_progress">ޕްރޮސެސިންގ</string>
  <string name="text_delete_failed">ޑިލީޓް ކުރަން ނާކާމިޔާބުވިއެވެ</string>
  <string name="text_edit_script">ސްކްރިޕްޓް އެޑިޓްކުރުން</string>
  <string name="text_run_script">ސްކްރިޕްޓް ހިންގާށެވެ</string>
  <string name="text_import_script">އިމްޕޯޓް ސްކްރިޕްޓް ފައިލް</string>
  <string name="text_root_record_out_file_type">ރޫޓް ރެކޯޑިންގ ޖެނެރޭޓް ކުރެވިފައިވާ ފައިލް ޓައިޕް</string>
  <string name="text_binary">ބައިނަރީ އެވެ</string>
  <string name="summary_stable_mode">ލޭއައުޓް އެނަލިސިސް އެނެބަލް ކުރުމުން މާ ސްޓޭބަލް ވިޔަސް މަދު އަދަދެއްގެ ސްކްރިޕްޓްތައް ރަނގަޅަށް މަސައްކަތް ނުކުރެވިދާނެ އެވެ. އެކްސެސިބިލިޓީ ޚިދުމަތަށް އަމަލުކުރަން ފެށުމަށްޓަކައި އަލުން ފެށުން</string>
  <string name="text_hideLaucher">ޑެސްކްޓޮޕް އައިކޮންތައް ފޮރުވުން (adb ނުވަތަ އެހެން ގޮތްތަކެއް މެދުވެރިކޮށް އެޕްތައް ލޯންޗްކުރުން)</string>
  <string name="text_stable_mode">ސްޓޭބަލް މޯޑެވެ</string>
  <string name="text_single_build_clean_mode">ސިންގަލް ފައިލް ޕެކޭޖިންގ ރިފްރެޝިންގ މޯޑް</string>
  <string name="summary_single_build_clean_mode">ހުޅުވުމަށްފަހު، އެއް ފައިލް ޕެކޭޖް ކުރާއިރު ޕެކޭޖިންގ ކޮންފިގްރޭޝަން ފައިލް ސޭވް ނުކުރެވޭނެ ކަމަށާއި، ސްޓާޓްއަޕް އިމޭޖާއި އެޕްލިކޭޝަން އައިކޮން ފަދަ ހުރިހާ ކޮންފިގްރޭޝަން މަޢުލޫމާތެއް ސޭވް ނުކުރެވޭނެ ކަމަށް ވެސް ވިދާޅުވިއެވެ.</string>
  <string name="text_directory">ފޯލްޑަރ އެވެ</string>
  <string name="text_file">ލިޔެކިޔުން</string>
  <string name="text_tutorial">ޓިއުޓޯރިއަލް އެވެ</string>
  <string name="text_community">މުޖުތަމަޢު</string>
  <string name="text_manage">ބެލެހެއްޓުން</string>
  <string name="not_login">އާދައިގެ ފަންކްޝަންތަކަށް ލޮގިން ކުރަން ނުޖެހެއެވެ</string>
  <string name="text_volume_down_control">ވޮލިއުމް ޑައުން ކީ ކޮންޓްރޯލް ކުރުން</string>
  <string name="text_auto_backup">އޮޓޮމެޓިކް ބެކަޕް ސްކްރިޕްޓް</string>
  <string name="text_others">އެހެން</string>
  <string name="text_import">އެތެރެކުރުން</string>
  <string name="text_click_too_frequently">ތިބާގެ އޮޕަރޭޝަން މާ އަވަސް ヽ(#`Д ́)ヽ</string>
  <string name="text_username">ޔޫޒަރނޭމް</string>
  <string name="text_password">ޕާސްވަރޑް</string>
  <string name="text_login">ލޮގް އިން ކޮށްލާށެވެ</string>
  <string name="text_forgot_password">ޕާސްވޯޑް ހަނދާން ނެތިއްޖެތަ؟</string>
  <string name="text_username_cannot_be_empty">ޔޫޒަރނޭމް ހުސް ނުކުރެވޭނެ</string>
  <string name="text_password_cannot_be_empty">ޕާސްވޯޑް ހުސް ނުކުރެވޭނެ</string>
  <string name="text_time">ވަގުތު</string>
  <string name="text_size">ސައިޒް</string>
  <string name="text_type">ވައްތަރު</string>
  <string name="text_editor_theme">އެޑިޓަރ ތީމްސް</string>
  <string name="text_jump_to_line">ލައިނަށް ފުންމާލާށެވެ</string>
  <string name="text_info">މަޢުލޫމާތު</string>
  <string name="text_find_or_replace">ހޯދުން/ބަދަލުކުރުން</string>
  <string name="text_copy_all">ހުރިހާ އެއްޗެއް ކޮޕީ ކޮށްލާށެވެ</string>
  <string name="text_copy_line">ކޮޕީ ރޯ</string>
  <string name="text_delete_line">ޑިލީޓް ރޯ</string>
  <string name="format_editor_info" formatted="false">އަކުރުތައް: %d \n : %d \n : %s</string>
  <string name="text_find">ހޯދުން</string>
  <string name="text_replace">ރިޕްލޭސް</string>
  <string name="text_regex">ގަވާއިދުން ފާޅުކުރުން</string>
  <string name="text_replace_all">ހުރިހާ އެއްޗެއް ބަދަލުކޮށްލާށެވެ</string>
  <string name="text_find_next">ތިރި</string>
  <string name="text_find_prev">މަތި</string>
  <string name="hint_regex">ގަވާއިދުން ސިންޓެކްސް ވާނީ ޖާވާސްކްރިޕްޓްގައި ހުންނަ ގޮތަށް، ކެޕްޗާ ކުރެވިފައިވާ މެޗުތަކުގެ ބަދަލުގައި $1~9 ބޭނުން ކުރެވިދާނެއެވެ</string>
  <string name="text_jump">ފުންމުން</string>
  <string name="text_jump_to_end">ފައިލް ނިމުމަކަށް ދާށެވެ</string>
  <string name="text_jump_to_start">ފައިލް ސްޓާޓް އަށް ދާށެވެ</string>
  <string name="text_jump_to_line_start">ލައިންގެ ފެށުމަށް ދާށެވެ</string>
  <string name="text_jump_to_line_end">ލައިންގެ ނިމުމާއި ހަމައަށް ދާށެވެ</string>
  <string name="text_exit_floating_window">ފްލޯޓިންގ ވިންޑޯއިން ނުކުމެލާށެވެ</string>
  <string name="text_select_save_path">އަށް ސޭވް ކޮށްލާށެވެ</string>
  <string name="text_cancel_download">ޑައުންލޯޑް ކެންސަލް ކުރާށެވެ</string>
  <string name="text_download">ޑައުންލޯޑް</string>
  <string name="format_file_downloaded" formatted="true">ފައިލް %s އަށް ސޭވް ކުރެވިފައިވެއެވެ</string>
  <string name="confirm_overwrite_file">އެ ފައިލް މިހާރުވެސް އެބައޮތް، އެ ފައިލް އޯވަރރައިޓް ކުރަން ބޭނުންވޭތޯ؟</string>
  <string name="text_select_file_to_upload">އަޕްލޯޑް ކުރަން ބޭނުންވާ ފައިލް ހޮވާށެވެ</string>
  <string name="text_select_file_to_import">އިމްޕޯޓް ކުރަން ބޭނުންވާ ފައިލް ހޮވާށެވެ</string>
  <string name="text_build_apk">ޕެކޭޖް ކުރެވިފައިވާ އެޕްސް</string>
  <string name="text_select">އިޚްތިޔާރު</string>
  <string name="text_output_apk_path">ޕެކޭޖްކޮށްފައިވާ އެޕް ސޭވް ލޮކޭޝަން</string>
  <string name="text_config">ކޮންފިގްރޭޓް ކުރުން</string>
  <string name="text_app_name">އެޕްލިކޭޝަން ނަން</string>
  <string name="text_package_name">ޕެކޭޖްތަކުގެ ނަންތަކެވެ</string>
  <string name="text_version_name">ވަރޝަން ނަމެވެ</string>
  <string name="text_version_code">ވަރޝަން ނަންބަރު</string>
  <string name="text_run_config">ކޮންފިގްރޭޝަން ހިންގާށެވެ</string>
  <string name="text_hideLogs">ލޮގް ފޮރުވުން</string>
  <string name="text_required_accessibility_service">އެކްސެސިބިލިޓީ ޚިދުމަތްތައް ބޭނުންވެއްޖެއެވެ</string>
  <string name="text_required_terminal_emulator">ޓާރމިނަލް އެމިއުލޭޓަރ އެވެ</string>
  <string name="text_required_background_start">ބެކްގްރައުންޑް ޕޮޕްއަޕް އިންޓަރފޭސްގެ ހުއްދަ ބޭނުންވެއެވެ</string>
  <string name="text_required_draw_overlay">ފްލޯޓިންގ ވިންޑޯގެ ހުއްދަ ބޭނުންވެއެވެ</string>
  <string name="text_volumeUpcontrol">ވޮލިއުމް އަޕް ކީ ޓު އެންޑް ޓާސްކް</string>
  <string name="text_splash_text">ސްޓާޓްއަޕް އިންޓަރފޭސް ޓެކްސްޓް</string>
  <string name="text_splash_icon">އިންޓަރފޭސް އައިކޮން ލޯންޗް ކުރާށެވެ</string>
  <string name="text_service_desc_text">އެކްސެސިބިލިޓީ ސާވިސަސްގެ ބަޔާން</string>
  <string name="text_sign">ސޮއި</string>
  <string name="text_sign_choose">ސޮއި ހޮވާށެވެ</string>
  <string name="text_sign_password">ސޮއި ސެޓްފިކެޓް ޕާސްވޯޑް</string>
  <string name="text_sign_password_input">ޕާސްވޯޑް ޔަގީން ކުރައްވާށެވެ</string>
  <string name="text_sign_manage">ސޮއި ބެލެހެއްޓުން</string>
  <string name="text_sign_key_add">ސޮއި އުފެއްދުން</string>
  <string name="text_sign_key_import">އިމްޕޯޓް ސޮއި</string>
  <string name="text_sign_hint_key_path">*ފައިލް ސޭވް ކުރުމުގެ މަގު</string>
  <string name="text_sign_hint_key_alias">*އެލިއާސް</string>
  <string name="text_sign_hint_key_password">*ޕާސްވަރޑް</string>
  <string name="text_sign_hint_key_year">*ސައްހަ މުއްދަތު (އަހަރު)</string>
  <string name="text_sign_key_year_def">25</string>
  <string name="text_sign_hint_key_country">*ގައުމުގެ ކޯޑް (XX)</string>
  <string name="text_sign_key_country_def">އީއެން</string>
  <string name="text_sign_hint_not_must">ތިރީގައި މިވަނީ އިޚްތިޔާރީ ޕެރެމިޓަރތަކެވެ</string>
  <string name="text_sign_hint_key_name">ނަން</string>
  <string name="text_sign_hint_key_org">އިންތިޒާމުކުރުން</string>
  <string name="text_sign_hint_key_org_unit">އޯގަނައިޒޭޝަނަލް ޔުނިޓް</string>
  <string name="text_sign_hint_key_province">ޕްރޮވިންސް</string>
  <string name="text_sign_hint_key_city">ޝަހަރު</string>
  <string name="text_sign_create_success">ކާމިޔާބުކަމާއެކު އުފެއްދިއްޖެއެވެ</string>
  <string name="text_sign_create_fail">އުފެއްދުމަށް ނާކާމިޔާބުވި</string>
  <string name="text_build_successfully">ކާމިޔާބުކަމާއެކު ޕެކޭޖް ކުރެވިއްޖެއެވެ</string>
  <string name="text_install">އެޅުން</string>
  <string name="text_build_failed">ޕެކޭޖިންގ ފެއިލްވެއްޖެ، އެރަރ މެސެޖެއް:</string>
  <string name="format_build_successfully" formatted="true">Apk ފައިލް %s އަށް ސޭވް ކުރެވިފައިވެއެވެ</string>
  <string name="apk_builder_prepare">ޑޮކިއުމެންޓް ތައްޔާރު ކުރުމުގެ މަސައްކަތް ދަނީ ކުރަމުންނެވެ</string>
  <string name="apk_builder_build">ބިނާ</string>
  <string name="apk_builder_package">ޕެކިންގ ހެދުން</string>
  <string name="apk_builder_sign">ސޮއިކުރުން</string>
  <string name="apk_builder_clean">ވަގުތީ ފައިލްތައް ސާފުކުރުން</string>
  <string name="text_source_file_path">ސްކްރިޕްޓް ފައިލް (ފޯލްޑަރ) މަގު</string>
  <string name="text_search">ހޯދުން</string>
  <string name="text_select_icon">އައިކޮން ހޮވާށެވެ</string>
  <string name="text_use_android_n_shortcut">އެންޑްރޮއިޑް 7.0 ޝޯޓްކަޓަށް އިތުރުކޮށްފި އެވެ</string>
  <string name="text_login_succeed">ލޮގިން ކާމިޔާބު ވެއްޖެއެވެ</string>
  <string name="text_login_fail">ލޮގިން ފެއިލްވެއްޖެއެވެ</string>
  <string name="text_reset_password">ޕާސްވޯޑް ރީސެޓް ކުރުން</string>
  <string name="text_logining">ލޮގިން އިން ވުމެވެ</string>
  <string name="text_register">ރަޖިސްޓްރީކުރުން</string>
  <string name="text_email">މެއިލް…</string>
  <string name="text_email_cannot_be_empty">އީމެއިލް ހުސް ނުކުރެވޭނެ</string>
  <string name="text_email_format_error">އީމެއިލް ފޯމެޓް ގޯސްވެއްޖެއެވެ</string>
  <string name="text_registering">ރަޖިސްޓްރީ ކުރުން</string>
  <string name="text_register_fail">ރަޖިސްޓްރޭޝަން ފެއިލްވެއްޖެ އެވެ</string>
  <string name="text_register_succeed">ރަޖިސްޓްރޭޝަން ކާމިޔާބު ވެއްޖެއެވެ</string>
  <string name="text_sample">ނަމޫނާ ކޯޑެވެ</string>
  <string name="text_reset_to_initial_content">އަސްލު ކޮންޓެންޓަށް ރީސެޓް ކުރާށެވެ</string>
  <string name="text_reset_succeed">ކާމިޔާބުކަމާއެކު ރީސެޓް ކުރުން</string>
  <string name="text_cannot_read_file">ފައިލް ކިޔަން ނޭނގޭ</string>
  <string name="text_service">ޚިދުމަތްކުރުން</string>
  <string name="text_notification_permission">ކިޔުމުގެ ހުއްދައަށް އަންގާށެވެ</string>
  <string name="text_open_main_activity">މައި އިންޓަރފޭސް ހުޅުވާލާށެވެ</string>
  <string name="text_generate_code">ކޯޑް ޖެނެރޭޓް ކުރުން</string>
  <string name="text_action">ޢަމަލު</string>
  <string name="text_click">ކްލިކް ކުރާށެވެ</string>
  <string name="text_long_click">ދިގުކޮށް ފިތާލާށެވެ (longClick)</string>
  <string name="text_set_text">ސެޓް ޓެކްސްޓް (ސެޓްޓެކްސްޓް)</string>
  <string name="text_scroll_forward">ކުރިއަށް/ވާތް/ތިރިއަށް ސްވައިޕް ކުރާށެވެ (ސްކްރޯލްފޯވާޑް)</string>
  <string name="text_scroll_backward">ފަހަތަށް/މައްޗަށް/ކަނާތްފަރާތަށް ސްކްރޯލްކުރުން (ފަހަތަށް ސްކްރޯލްކުރުން)</string>
  <string name="text_find_one">އެކަކު ފެންނަންދެން (findOne)</string>
  <string name="text_until_find">ހުރިހާ އެއްޗެއް ފެންނަންދެން (ހޯދާންދެން)</string>
  <string name="text_wait_for">ކޮންޓްރޯލް ފެންނަންދެން މަޑުކޮށްލާށެވެ (waitFor)</string>
  <string name="text_selector_exists">ކޮންޓްރޯލް (އޮތް) އޮތްތޯ ޙުކުމްކުރުން .</string>
  <string name="text_options">އިޚްތިޔާރުތައް</string>
  <string name="text_using_id_selector">އައިޑީ ބޭނުންކޮށްގެން ހޮވާށެވެ</string>
  <string name="text_using_text_selector">ލިޔުން ހޮވުން (ޓެކްސްޓް) ބޭނުންކުރުން .</string>
  <string name="text_using_desc_selector">ޑިސްކްރިޕްޝަން (ޑެސްކް) ބޭނުންކޮށްގެން ހޮވާށެވެ.</string>
  <string name="text_generate">ޖެނެރޭޓް</string>
  <string name="text_generate_fail">ބިލްޑް ފެއިލް ވެއްޖެ o(╥*╥)o</string>
  <string name="text_documentation_source">ޑޮކިއުމަންޓޭޝަން ސޯސް</string>
  <string name="text_send">ފޮނުވުން</string>
  <string name="text_timed_task">ޓައިމްޑް ޓާސްކް</string>
  <string name="text_running_task">ހިނގަމުންދާ ސްކްރިޕްޓް</string>
  <string name="text_done">ނިންމުން</string>
  <string name="text_disposable_task">ދުއްވާނީ އެންމެ ފަހަރަކުއެވެ</string>
  <string name="text_daily_task">ދުވާލަކު ދުއްވައެވެ</string>
  <string name="text_weekly_task">ހަފްތާއަކުން ދުއްވައެވެ</string>
  <string name="text_timing">ޓައިމިންގ އެވެ</string>
  <string name="text_disposable_task_time_before_now">މިހާރު ބޭނުންކުރާ ވަގުތަށްވުރެ ޓާސްކް ވަގުތު މަދުވެއެވެ</string>
  <string name="text_next_run_time">ދެން ދުވުން</string>
  <string name="text_day1">ހޯމަ ދުވަހު އެވެ</string>
  <string name="text_day2">އަންގާރަ ދުވަސް</string>
  <string name="text_day3">ބުދަ ދުވަސް</string>
  <string name="text_day4">ބުރާސްފަތި</string>
  <string name="text_day5">ހުކުރު ދުވަސް</string>
  <string name="text_day6">ހޮނިހިރު ދުވަސް</string>
  <string name="text_day7">އާދީއްތަ ދުވަސް</string>
  <string name="text_weekly_task_should_check_day_of_week">ހަފްތާގެ މަދުވެގެން އެއް ދުވަސް ހޮވާށެވެ</string>
  <string name="no_apk_builder_plugin">ޕެކޭޖްކޮށްފައިވާ ޕްލަގިން އިންސްޓޯލްކޮށްފައި ނުވާތީ މިހާރު ޑައުންލޯޑް ކުރަން ބޭނުން ހެއްޔެވެ؟</string>
  <string name="text_apk_builder_plugin_unavailable">ޕެކޭޖް ޕްލަގިން ނުލިބޭ</string>
  <string name="text_detail">ތަފްޞީލު</string>
  <string name="text_open">ހުޅުވުން</string>
  <string name="text_generated_code">ނަތީޖާ ހޯދުން</string>
  <string name="text_copy">ކޮޕީ</string>
  <string name="text_should_not_be_empty">ހުސް ނުވެވޭނެ</string>
  <string name="text_icon">އައިކޮން އެވެ</string>
  <string name="text_device_not_rooted">ޑިވައިސް ރޫޓް ނުކުރެވެއެވެ</string>
  <string name="prompt_device_not_rooted">އެއީ ތިބާގެ ޑިވައިސް ރޫޓް ނުވާކަން ފާހަގަކުރެވި، ރެކޯޑިންގ ސްކްރިޕްޓަށް ރޫޓް ޕްރިވިލެޖް ބޭނުންވާކަން، ކުރިއަށް ގެންދަން ބޭނުންވޭތޯ؟</string>
  <string name="text_device_rooted">އަދިވެސް ރެކޯޑް ކުރަން ޖެހެ އެވެ</string>
  <string name="text_community_category">ސެކްޝަން</string>
  <string name="text_community_unread">ނުކިޔަވާ</string>
  <string name="text_community_recent">އަޕްޑޭޓް</string>
  <string name="text_community_popular">މަޝްހޫރު</string>
  <string name="text_community_tags">މައުޟޫއު</string>
  <string name="nodebb_error_invalid_login_credentials">ސައްހަ ނޫން ލޮގިން ކްރެޑިޝަންސް</string>
  <string name="nodebb_error_change_password_error_length">ޕާސްވޯޑް މާ ކުރު ވެއްޖެއެވެ</string>
  <string name="nodebb_error_email_taken">މެއިލް ފޮށި ހިސޯރުކޮށްފައި ހުރެއެވެ</string>
  <string name="nodebb_error_change_password_error_match">އެންޓަރ ކުރި ދެ ޕާސްވޯޑް އެއްގޮތެއް ނުވެއެވެ</string>
  <string name="text_logout">ސައިން އައުޓް ކުރާށެވެ</string>
  <string name="nodebb_error_forbidden">އޮޕަރޭޝަން ފެއިލްވެއްޖެ، ފަހުން އަލުން މަސައްކަތް ކުރައްވާ</string>
  <string name="description_stable_mode">ސްޓޭބަލް މޯޑުން ލޭއައުޓް ހިފާއިރު ސްކްރިޕްޓް ސްޓޭބަލް ކޮށްދެނީ ލޭއައުޓްގެ ތަފްސީލުތައް ދޫކޮށްލާފައި، އެކަމަކު ހަމަ އެއާއެކު ސްކްރީންގައި މަދު ކޮންޓްރޯލްތަކެއް ގެނެސްގެންނެވެ. \n \n nއެކްސެސިބިލިޓީ ޚިދުމަތަށް އަމަލުކުރަން ފެށުމަށްޓަކައި އަލުން ފެށުން.</string>
  <string name="text_size_min_value">2</string>
  <string name="text_size_max_value">60</string>
  <string name="text_text_size">ފޮންޓް ސައިޒް</string>
  <string name="text_preview">ޕްރިވިއު</string>
  <string name="text_size_current_value" formatted="true">ފޮންޓް ސައިޒް: %d</string>
  <string name="error_regex_find_prev">ގަވާއިދުން ޚިޔާލު ފާޅުކުރުމަކީ ކުރިއަށް ބަލާކަށް ނުކެރޭނެ</string>
  <string name="text_notification">ނޯޓިސް</string>
  <string name="apk_builder_plugin_version_too_low">ޕެކޭޖް ކުރެވިފައިވާ ޕްލަގް-އިން ވަރޝަން މާ ދަށްވުމުން، ޕެކޭޖް ކުރެވިފައިވާ އެޕްލިކޭޝަން ރަނގަޅަށް ނުހިނގާފާނެއެވެ. މިހާރު ޕެކޭޖްކޮށްފައިވާ ޕްލަގް-އިންގެ އެއާ ގުޅޭ ވަރޝަން ޑައުންލޯޑް ކުރަން ބޭނުންވޭތޯ؟</string>
  <string name="text_inspect_layout_bounds">ލޭއައުޓް ރޭންޖް ތަޙުލީލުކުރުން</string>
  <string name="text_inspect_layout_hierarchy">ލޭއައުޓް ހައިރާކީ</string>
  <string name="text_inspect_layout">ލޭއައުޓް ތަޙުލީލުކުރުން</string>
  <string name="text_pointer_location">ޕޮއިންޓަރ ޕޮޒިޝަން [ރޫޓް] އެވެ.</string>
  <string name="text_change_script_dir">ސްކްރިޕްޓް ފޯލްޑަރ މަގު ބަދަލުކުރުން</string>
  <string name="text_error_copy_file" formatted="true">ގޯހެއް ދިމާވެއްޖެ: %s</string>
  <string name="text_debug">ޑީބަގިން ކުރުން</string>
  <string name="text_set_breakpoint">ބްރޭކްޕޮއިންޓެވެ</string>
  <string name="text_launch_debugger">ޑީބަގް ކުރަން ފަށާށެވެ</string>
  <string name="text_debug_step_out">ފުންމާލައިގެން ނުކުމެއްޖެއެވެ</string>
  <string name="text_debug_step_into">ވަނުން</string>
  <string name="text_debug_step_over">އެއް ފިޔަވަޅެއް</string>
  <string name="text_debug_resume_script">ކުރިއަށްގެންދިޔުން</string>
  <string name="text_stop">ހުއްޓުން</string>
  <string name="text_new_watching_variable">ބަލަން ބޭނުންވާ ވެރިއޭބަލް ނުވަތަ އެކްސްޕްރެޝަނެއް އިތުރުކުރުން</string>
  <string name="text_variable_or_expr">ވެރިއޭބަލް ނުވަތަ އެކްސްޕްރެޝަން</string>
  <string name="text_remove_all_breakpoints">ހުރިހާ ބްރޭކްޕޮއިންޓްތަކެއް ނައްތާލާށެވެ</string>
  <string name="hint_long_click_run_to_debug">އަދި \"ރަން\" އައިކޮން އަށް ދިގު މުއްދަތަކަށް ފިތާލުމުން ވެސް ޑީބަގިން ފަށައިގަނެވޭނެ އެވެ</string>
  <string name="format_debug_bar_title">ޑީބަގް [ %s ] އެވެ.</string>
  <string name="text_copy_value">ކޮޕީ ވެލިއު</string>
  <string name="text_execute_code">އެގްޒެކެޓް ކޯޑް</string>
  <string name="text_code">ކޯޑެވެ</string>
  <string name="text_result">ނަތީޖާ</string>
  <string name="text_close">ބަންދުކުރުން</string>
  <string name="text_execute">ތަންފީޒުކުރުން</string>
  <string name="error_connect_to_remote">ގުޅުން ނާކާމިޔާބު: %s</string>
  <string name="text_are_you_sure_to_delete">%s ޑިލީޓް ކުރަން ބޭނުންވާކަން ޔަގީން ހެއްޔެވެ</string>
  <string name="text_run_on_broadcast">ވަކި އިވެންޓަކުން (ބްރޯޑްކާސްޓް) އޮޕަރޭޝަން ޓްރިގަރ ކުރެއެވެ</string>
  <string name="text_search_java_class">ޖާވާ ޕެކޭޖްތައް/ކްލާސްތައް ހޯދުން</string>
  <string name="text_class_or_package_name">ކްލާސް/ޕެކޭޖް ނަން</string>
  <string name="text_view_docs">ލިޔެކިޔުންތައް ބަލާލުން</string>
  <string name="text_market">މާރުކޭޓު</string>
  <string name="text_launch_config">ކޮންފިގްރޭޝަން ހިންގާށެވެ</string>
  <string name="text_main_file_name">މައިގަނޑު ސްކްރިޕްޓް ނަމެވެ</string>
  <string name="text_project">ޕްރޮޖެކްޓް</string>
  <string name="text_project_location">ޕްރޮޖެކްޓް ކުރެވިފައިވާ ތަން</string>
  <string name="text_new_project">އައު މަޝްރޫޢެއް</string>
  <string name="text_js_file">ޖޭއެސް ފައިލް އެވެ</string>
  <string name="text_invalid_project">ސައްހަ ނޫން އެއްޗެއް</string>
  <string name="text_invalid_project_config">ކޮންފިގްރޭޝަން ފައިލެއް ނެތެވެ</string>
  <string name="text_foreground_service">ރިސެޕްޝަން</string>
  <string name="foreground_notification_channel_name">ފްރަންޓް ޑެސްކް ސާވިސް ނޮޓިފިކޭޝަން</string>
  <string name="foreground_notification_title">Autox.js ދުއްވަމުން ގެންދެއެވެ</string>
  <string name="foreground_notification_text">މައި އިންޓަރފޭސްއަށް ވަނުމަށް ކްލިކް ކުރާށެވެ</string>
  <string name="text_run_on_other_broadcast">އެހެން އިވެންޓްތައް (ބްރޯޑްކާސްޓް)</string>
  <string name="text_broadcast_action">ބްރޯޑްކާސްޓް އެކްޝަން</string>
  <string name="text_run_on_boot">ބޫޓް ކުރާއިރު</string>
  <string name="text_run_on_screen_on">ސްކްރީން އޮން ކޮށްފައި ހުންނައިރު</string>
  <string name="text_run_on_screen_off">ސްކްރީން ނިއްވާފައި ހުންނަ ވަގުތުގައެވެ</string>
  <string name="error_empty_selection">ބްރޯޑްކާސްޓް އިވެންޓް އޮޕްޝަނެއް ހޮއްވަވާށެވެ</string>
  <string name="text_run_on_battery_change">ބާރު ބަދަލުވުމުންނެވެ</string>
  <string name="text_run_on_screen_unlock">ސްކްރީން އަންލޮކް ކޮށްފައި ހުންނައިރު</string>
  <string name="text_run_on_power_connect">ކަރަންޓް ގުޅާފައި އޮތުމުންނެވެ</string>
  <string name="text_run_on_power_disconnect">ކަރަންޓް ކަނޑާލާ ވަގުތުގައެވެ</string>
  <string name="text_run_on_conn_change">ނެޓްވޯކް ކަނެކްޝަން ބަދަލުވުމުންނެވެ</string>
  <string name="text_run_on_package_install">އާ އެޕް އިންސްޓޯލް ކުރުމުންނެވެ</string>
  <string name="text_run_on_package_uninstall">އެޕް އަންއިންސްޓޯލް ކުރާއިރު</string>
  <string name="text_run_on_package_update">އަޕްޑޭޓެއް އެޕްލައި ކުރާއިރު</string>
  <string name="text_run_on_headset_plug">ހެޑްސެޓް ޕްލަގްކޮށް ޕްލަގް ނަގާއިރު</string>
  <string name="text_run_on_time_tick">ކޮންމެ މިނެޓަކުންނެވެ</string>
  <string name="text_run_on_config_change">ވަކި ސެޓިންގްސްތަކެއް (ސްކްރީން އޮރިއެންޓޭޝަން، ރީޖަން ފަދަ) ބަދަލުކުރާއިރު</string>
  <string name="error_pattern_syntax">ގަވާއިދުން އެކްސްޕްރެޝަން އެރަރ</string>
  <string name="text_invalid_package_name">ގަވާއިދާ ހިލާފަށް ޕެކޭޖް ނަމެވެ</string>
  <string name="error_cannot_rename">ނަން ބަދަލުކުރުން ނާކާމިޔާބުވިއެވެ</string>
  <string name="text_project_save_success">ޕްރޮޖެކްޓް %s އަށް ސޭވް ކުރެވިއްޖެއެވެ</string>
  <string name="text_project_save_error">ޕްރޮޖެކްޓް ސޭވް ނާކާމިޔާބު %s</string>
  <string name="text_night_mode">ނައިޓް މޯޑެވެ</string>
  <string name="text_run_on_startup">Autox.js ފެށޭއިރު</string>
  <string name="text_usage_stats_permission">ޔޫސޭޖް އެކްސެސް</string>
  <string name="description_usage_stats_permission">\"ވިއު ޔޫސޭޖް ސްޓެޓިސްޓިކްސް\" ހުއްދަ މެދުވެރިކޮށް، މި ޑިވައިސްގެ މާޒީގެ އެޕްލިކޭޝަން ޔޫސް ހޯދައި، މިހާރު ބޭނުންކުރާ އެޕްލިކޭޝަން (currentPackage) ހޯދުން މާ ޞައްޙަ ވެގެންދާނެއެވެ.</string>
  <string name="text_open_with">ހުޅުވާލެވިފައިވާ ގޮތަކަށެވެ</string>
  <string name="text_root">މޫ</string>
  <string name="text_no_root">ރޫޓް ފްރީ އެވެ</string>
  <string name="text_star">ހަވާލުވުން</string>
  <string name="text_upvote">ވަރަށް ކަމުދާ</string>
  <string name="text_downvote">ފިޔަވަޅު އަޅާށެވެ</string>
  <string name="textSizePlus">+</string>
  <string name="textSizeMinus">-</string>
  <plurals name="air_error_short_description" key="air_error_short_description"> 
    <item quantity="one">ޑިސްކްރިޕްޝަން މަދުވެގެން %d އަކުރު ދިގުވާން ޖެހެއެވެ.</item>  
    <item quantity="other">ޑިސްކްރިޕްޝަން މަދުވެގެން %d އަކުރު ދިގުވާން ޖެހެއެވެ.</item> 
  </plurals>
  <string name="air_title_report_issue">އެރަރ ރިޕޯޓް</string>
  <string name="air_title_issue">ސުވާލު</string>
  <string name="air_label_issue_title">މަޤާމު</string>
  <string name="air_label_issue_description">ސިފަކުރުން</string>
  <string name="air_label_device_info">ޑިވައިސްގެ މަޢުލޫމާތު</string>
  <string name="air_label_use_email">އީމެއިލް މެދުވެރިކޮށް ފޮނުވާށެވެ</string>
  <string name="air_label_use_guest">ނަން ނުޖެހި ފޮނުވާށެވެ</string>
  <string name="air_label_email">މެއިލް…</string>
  <string name="air_label_email_optional">ގުޅޭނެ އީމެއިލް (އިޚްތިޔާރީ)</string>
  <string name="air_error_no_title">ސުވާލެއް ލިޔުއްވާށެވެ</string>
  <string name="air_error_no_description">ތަފްސީލީ ތަފްސީލެއް ލިޔުއްވާށެވެ</string>
  <string name="air_error_no_username">ސައްހަ ގިތަބް ޔޫޒަރނޭމް އެއް ލިޔެލާށެވެ</string>
  <string name="air_error_no_password">ރަނގަޅު ޕާސްވޯޑް ޖައްސަވާށެވެ</string>
  <string name="air_error_no_email">އީމެއިލް އިންޕުޓް ކުރައްވާށެވެ</string>
  <string name="air_dialog_title_loading">ގިޓްހަބަށް އަޕްލޯޑް ކުރަނީ...</string>
  <string name="air_dialog_title_failed">ބަގް ރިޕޯޓް ފޮނުވަން ނާކާމިޔާބުވިއެވެ</string>
  <string name="air_dialog_description_failed_wrong_credentials">ގޯސް ޔޫޒަރނޭމް ނުވަތަ ޕާސްވޯޑެއް</string>
  <string name="air_dialog_description_failed_invalid_token">ސައްހަ ނޫން އެކްސެސް ޓޯކަން ސޮފްޓްވެއާ ޑިވެލޮޕަރަށް ގުޅާށެވެ.</string>
  <string name="air_dialog_description_failed_issues_not_available">މައްސަލަތައް ލިބިފައި ނުވާތީ ސޮފްޓްވެއާ ޑިވެލޮޕަރާއި ގުޅުއްވުން އެދެމެވެ.</string>
  <string name="air_dialog_description_failed_unknown">ނޭނގޭ ގޯހެއް</string>
  <string name="air_dialog_action_failed">އެންމެ ރަނގަޅު</string>
  <string name="text_template_apk_not_found">ޓެމްޕްލޭޓް apk ނުފެނިއްޖެނަމަ، \"build/outputs/apk/common/release\" ޑައިރެކްޓަރީގައިވާ apk ފައިލް \"app/src/main/assets\" އަށް ކޮޕީކޮށް \"template.apk\" ގެ ނަން ދީފައި އަލުން ބިނާކުރައްވާށެވެ</string>
  <string name="desc_back">ފަހަތް</string>
  <string name="desc_add_icon">އެޑް އައިކޮން</string>
  <string name="text_default_signature">ޑިފޯލްޓް ސޮއި</string>
  <string name="desc_done">ނިންމުން</string>
  <string name="apk_icon">އޭޕީކޭ އައިކޮން</string>
  <string name="text_packaging_options">ކޮމްޕައިލް އޮޕްޝަންސް އެވެ</string>
  <string name="text_verification_failed">ވެރިފިކޭޝަން ފެއިލްވެއްޖެއެވެ</string>
  <string name="text_WebX">ވެބްއެކްސް އެވެ</string>
  <string name="text_file_manager_permission">ހުރިހާ ފައިލެއް އެކްސެސް ކުރެވޭނެއެވެ</string>
  <string name="text_fullscreen">ފުލް ސްކްރީން މޯޑް ޓޮގްލް ކުރުން 1</string>
  <string name="text_drawer_right">ކަނާތްފަރާތުގެ މެނޫ ހުޅުވާލާށެވެ</string>
  <string name="text_required_default_paddle_ocr_model">ޕެކޭޖް ޑިފޯލްޓް ޕެޑަލްއޯސީއާރު ޓްރެއިނިންގ ޑޭޓާ</string>
  <string name="switch_fullscreen">ފުލް ސްކްރީން މޯޑް ޓޮގްލް ކުރާށެވެ</string>
  <string name="text_custom_ocr_model_path">ޕެކޭޖް ކަސްޓަމް އޯސީއާރު މޮޑެލް ޑައިރެކްޓަރީ</string>
  <string name="desc_more">އިތުރަށް</string>
  <string name="text_switch_web_kernel">ވެބް ކާރނަލް ބަދަލުކުރުން</string>
  <string name="text_favorites_management">ކަލެކްޝަން މެނޭޖްމަންޓް</string>
  <string name="text_switch_timed_task_scheduler">ޓައިމްޑް ޓާސްކް ޝެޑިއުލަރ ބަދަލުކުރުން</string>
  <string name="text_go_to_open">ހުޅުވުމަށްޓަކައެވެ</string>
  <string name="text_home">ގެ</string>
  <string name="text_management">ބެލެހެއްޓުން</string>
  <string name="text_document">ލިޔެކިޔުންތައް ހެދުން</string>
  <string name="text_open_usb_debug">ޔޫއެސްބީ ޑީބަގިންގ އޮން ކޮށްލާށެވެ</string>
  <string name="text_scan_qr">ސްކޭން ކޯޑް</string>
  <string name="text_connect_failed">ގުޅުން ނާކާމިޔާބުވެއްޖެ: %s , ކޮމްޕިއުޓަރ ފަޔަރވޯލް އިން ޕޯޓް 9317 ހުއްދަކުރާތޯއާއި، އެއީ ހަމަ ލޯކަލް އޭރިއާ ނެޓްވޯކެއްގައި ހުރިތޯއާއި، ވީއެސް ކޯޑް ޕްލަގް-އިން އިން ޚިދުމަތް ހުޅުވާފައިވާތޯ ބަލާށެވެ...</string>
  <string name="text_start_service_failed">ޚިދުމަތް ފެށުމަށް ނާކާމިޔާބުވި: %s</string>
  <string name="text_unsupported_qr_code">މި ފޯމެޓްގައި ހުންނަ ކިއުއާރު ކޯޑްތަކަށް ސަޕޯޓް ނުކުރެއެވެ</string>
  <string name="text_connected_to_computer">ކޮމްޕިއުޓަރާއި ގުޅިފައި ހުރެއެވެ</string>
  <string name="text_press_again_to_exit_app">އެޕް އިން ނުކުތުމަށް އަނެއްކާވެސް ފިއްތާލާށެވެ</string>
  <string name="text_app_download_link">ޑައުންލޯޑް ލިންކް</string>
  <string name="text_project_link">ޕްރޮޖެކްޓް އެޑްރެސް</string>
  <string name="text_release_date">ޝާއިޢުކުރެވިފައިވަނީ: %s</string>
  <string name="text_checking_for_updates">އަޕްޑޭޓްތައް ހުރިތޯ ބެލުން</string>
  <string name="text_update_information_not_found">އަޕްޑޭޓް މައުލޫމާތެއް ނުފެނެއެވެ</string>
  <string name="text_please_enable_external_storage">ފުރަތަމަ އެކްސްޓަރނަލް ސްޓޯރޭޖް ހުއްދަ އެނެބަލް ކުރައްވާށެވެ</string>
  <string name="text_app_detail_settings">އެޕް ޑީޓެއިލްސް ސެޓިންގސް</string>
  <string name="text_logcat">ވަކަރު ބުރި</string>
  <string name="text_menu">މެނޫ އެވެ</string>
  <string name="text_exit_search">ސަރޗް އިން ނުކުތުން</string>
  <string name="text_licenses_other">ނޮން އޯޕަން ސޯސް ލައިސަންސް</string>
  <string name="text_save_successfully">ކާމިޔާބުކަމާއެކު ސަލާމަތް ކުރެވުނެވެ</string>
  <string name="text_invalid_config">ސައްހަ ނޫން ކޮންފިގްރޭޝަން</string>
  <string name="text_abi">ސަޕޯޓްކުރާ އޭބީއައިތައް (ކޮމާތަކުން ވަކިކޮށްފައި)</string>
  <string name="text_save_project_to">ޕްރޮޖެކްޓް ސޭވްކޮށްފައިވަނީ: %s</string>
  <string name="text_hide_and_show">ފޮރުވުން/ދައްކާށެވެ</string>
  <string name="text_browser_open">ބްރައުޒާ ހުޅުވާލާށެވެ</string>
  <string name="text_do_nothing">އެއްވެސް ކަމެއް ނުކުރާށެވެ</string>
  <string name="text_copy_to_new_folder">އަސްލު ފޯލްޑަރުގައި ހުރި ތަކެތި އާ ފޯލްޑަރަށް ކޮޕީ ކުރާށެވެ</string>
  <string name="text_move_new_folder">އަސްލު ފޯލްޑަރުގައި ހުރި ތަކެތި އާ ފޯލްޑަރަށް ބަދަލުކޮށްލާށެވެ</string>
  <string name="text_accessibility_service_is_not_enable">އެކްސެސިބިލިޓީގެ ހިދުމަތް ދިއްލާފައެއް ނުވެއެވެ</string>
  <string name="text_set_successfully">ކާމިޔާބުކަމާއެކު ސެޓް ކޮށްފި އެވެ</string>
  <string name="text_reconnecting">އަލުން ގުޅުވުން</string>
  <string name="text_connecting">ގުޅުވަމުންނެވެ</string>
  <string name="text_handshake_failed">އަތް ދިއްކުރުން ނާކާމިޔާބުވިއެވެ</string>
</resources>
