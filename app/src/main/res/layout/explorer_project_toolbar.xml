<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingBottom="3dp"
    android:paddingLeft="12dp"
    android:paddingTop="3dp">

    <ImageView
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:src="@drawable/ic_project"/>

    <TextView
        android:id="@+id/project_name"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="8dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:text="Project"
        android:textColor="#606366"
        android:textSize="18sp"/>


    <ImageView
        android:id="@+id/run"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginRight="2dp"
        android:background="?selectableItemBackgroundBorderless"
        android:padding="6dp"
        android:src="@drawable/ic_play_fill"
        app:tint="#606366"/>

    <ImageView
        android:id="@+id/build"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginRight="2dp"
        android:background="?selectableItemBackgroundBorderless"
        android:padding="6dp"
        android:src="@drawable/ic_android_fill"
        app:tint="#606366"/>

    <ImageView
        android:id="@+id/sync"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginRight="2dp"
        android:background="?selectableItemBackgroundBorderless"
        android:padding="6dp"
        android:src="@drawable/ic_sync"
        app:tint="#606366"/>

</LinearLayout>