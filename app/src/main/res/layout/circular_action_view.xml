<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <com.makeramen.roundedimageview.RoundedImageView
        android:id="@+id/icon"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:background="@drawable/circle_white"
        android:padding="@dimen/padding_circular_menu_normal"
        android:src="@drawable/ic_android_eat_js"
        app:riv_corner_radius="16dp"
        app:riv_mutate_background="true"
        app:riv_oval="true"/>
</LinearLayout>