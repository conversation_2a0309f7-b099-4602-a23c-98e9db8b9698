package org.autojs.autojs.build

import org.junit.Test
import org.junit.Assert.*
import java.io.File
import java.util.zip.ZipEntry
import java.util.zip.ZipFile

/**
 * APK结构验证测试
 * 用于验证生成的APK是否符合Android R+要求
 */
class ApkStructureTest {
    
    @Test
    fun testResourcesArscCompression() {
        // 这个测试需要一个实际的APK文件来验证
        // 在实际使用中，可以在构建完成后调用此方法验证
        
        val apkPath = "path/to/test.apk"  // 替换为实际APK路径
        if (!File(apkPath).exists()) {
            println("APK file not found, skipping test")
            return
        }
        
        verifyApkStructure(File(apkPath))
    }
    
    /**
     * 验证APK结构是否符合Android R+要求
     */
    fun verifyApkStructure(apkFile: File): ApkVerificationResult {
        val result = ApkVerificationResult()
        
        try {
            ZipFile(apkFile).use { zipFile ->
                zipFile.entries().asSequence().forEach { entry ->
                    when (entry.name) {
                        "resources.arsc" -> {
                            result.hasResourcesArsc = true
                            result.resourcesArscCompressed = entry.method == ZipEntry.DEFLATED
                            result.resourcesArscSize = entry.size
                            result.resourcesArscCompressedSize = entry.compressedSize
                            
                            // 检查4字节对齐
                            // 注意：这里只是简单检查，实际的对齐检查需要更复杂的逻辑
                            result.resourcesArscAligned = checkAlignment(entry)
                        }
                        "AndroidManifest.xml" -> {
                            result.hasManifest = true
                        }
                        "META-INF/MANIFEST.MF" -> {
                            result.hasSigning = true
                        }
                        "classes.dex" -> {
                            result.hasDex = true
                        }
                    }
                    
                    // 检查assets目录
                    if (entry.name.startsWith("assets/project/")) {
                        result.hasProjectAssets = true
                    }
                    
                    // 检查lib目录
                    if (entry.name.startsWith("lib/")) {
                        result.hasNativeLibs = true
                    }
                }
            }
        } catch (e: Exception) {
            result.error = e.message
        }
        
        return result
    }
    
    private fun checkAlignment(entry: ZipEntry): Boolean {
        // 简化的对齐检查
        // 实际实现需要检查文件在ZIP中的偏移量
        return entry.method == ZipEntry.STORED
    }
    
    /**
     * 打印APK验证结果
     */
    fun printVerificationResult(result: ApkVerificationResult) {
        println("=== APK Structure Verification ===")
        println("Has resources.arsc: ${result.hasResourcesArsc}")
        if (result.hasResourcesArsc) {
            println("resources.arsc compressed: ${result.resourcesArscCompressed}")
            println("resources.arsc size: ${result.resourcesArscSize}")
            println("resources.arsc compressed size: ${result.resourcesArscCompressedSize}")
            println("resources.arsc aligned: ${result.resourcesArscAligned}")
            
            if (result.resourcesArscCompressed) {
                println("❌ ERROR: resources.arsc should not be compressed for Android R+")
            } else {
                println("✅ OK: resources.arsc is stored uncompressed")
            }
            
            if (!result.resourcesArscAligned) {
                println("❌ ERROR: resources.arsc should be 4-byte aligned")
            } else {
                println("✅ OK: resources.arsc appears to be aligned")
            }
        }
        
        println("Has AndroidManifest.xml: ${result.hasManifest}")
        println("Has signing info: ${result.hasSigning}")
        println("Has DEX files: ${result.hasDex}")
        println("Has project assets: ${result.hasProjectAssets}")
        println("Has native libs: ${result.hasNativeLibs}")
        
        if (result.error != null) {
            println("❌ ERROR: ${result.error}")
        }
        
        val androidRCompatible = result.hasResourcesArsc && 
                                !result.resourcesArscCompressed && 
                                result.resourcesArscAligned
        
        if (androidRCompatible) {
            println("✅ APK appears to be Android R+ compatible")
        } else {
            println("❌ APK may not be Android R+ compatible")
        }
    }
}

/**
 * APK验证结果
 */
data class ApkVerificationResult(
    var hasResourcesArsc: Boolean = false,
    var resourcesArscCompressed: Boolean = false,
    var resourcesArscSize: Long = 0,
    var resourcesArscCompressedSize: Long = 0,
    var resourcesArscAligned: Boolean = false,
    var hasManifest: Boolean = false,
    var hasSigning: Boolean = false,
    var hasDex: Boolean = false,
    var hasProjectAssets: Boolean = false,
    var hasNativeLibs: Boolean = false,
    var error: String? = null
)
