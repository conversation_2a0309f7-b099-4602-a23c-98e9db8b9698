# AutoX.js APK Building Fix for Android R+

## 问题描述
AutoX.js在打包脚本应用时，生成的APK在Android 11+ (API 30+) 设备上无法安装，错误信息：
```
Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary.
```

## 根本原因
Android R+ (API 30+) 要求APK中的resources.arsc文件必须：
1. 以未压缩格式存储 (STORED method)
2. 在4字节边界上对齐

## 修复方案

### 1. 主应用和模板APK构建配置
已经正确配置了Android R+要求：
- `app/build.gradle.kts`: `androidResources { noCompress += ".arsc" }`
- `inrt/build.gradle.kts`: `androidResources { noCompress += ".arsc" }`

### 2. APK签名过程修复
在以下文件中添加了Android R+支持：

#### ApkBuilder.kt
- `doFile()` 方法：确保resources.arsc使用STORED方法
- `doFileWithAlignment()` 方法：处理4字节对齐
- `inZipWithAlignment()` 方法：优化文件处理顺序

#### DefaultSign.kt
- `doFile()` 方法：确保resources.arsc使用STORED方法

#### ZipSigner.java
- 已有正确的Android R+修复
- `setCompression(0)` 确保resources.arsc未压缩

#### ZioEntry.java
- 已有自动4字节对齐逻辑
- 在compression == 0时自动计算对齐字节

### 3. 关键修复点

1. **资源文件压缩设置**：
```kotlin
if (name == "resources.arsc") {
    entry.method = ZipEntry.STORED
    val fileBytes = f.readBytes()
    entry.size = fileBytes.size.toLong()
    val crc32 = CRC32()
    crc32.update(fileBytes)
    entry.crc = crc32.value
}
```

2. **4字节对齐**：
ZioEntry类自动处理对齐：
```java
if (compression == 0) {
    long dataPos = output.getFilePointer() + 2 + filename.length() + extraData.length;
    short dataPosMod4 = (short) (dataPos % 4);
    if (dataPosMod4 > 0) {
        numAlignBytes = (short) (4 - dataPosMod4);
    }
}
```

## 测试验证

### 构建测试APK
1. 构建AutoX.js主应用
2. 使用应用打包一个简单脚本
3. 在Android 11+设备上安装测试

### 验证步骤
1. 检查生成的APK中resources.arsc是否未压缩
2. 验证文件在ZIP中的对齐情况
3. 在目标设备上安装测试

## 相关文件
- `app/src/main/java/org/autojs/autojs/build/ApkBuilder.kt`
- `app/src/main/java/org/autojs/autojs/build/DefaultSign.kt`
- `app/src/main/java/org/autojs/autojs/build/apksigner/ZipSigner.java`
- `app/src/main/java/org/autojs/autojs/build/apksigner/zipio/ZioEntry.java`
- `apkbuilder/src/main/java/com/stardust/autojs/apkbuilder/ApkPackager.java`

## 注意事项
- 确保模板APK (template.apk) 是从正确配置的inrt模块构建的
- TinySign库可能需要更新以支持Android R+要求
- 建议使用ZipSigner而不是TinySign进行APK签名
