# AutoX.js 现代化APK构建系统

## 重构概述

### 问题分析
原有的APK构建系统存在以下问题：
1. **多套签名实现**：ZipSigner、DefaultSign、TinySign三套不同的签名方案
2. **Android R+兼容性问题**：手动处理resources.arsc压缩和对齐容易出错
3. **过时的工具链**：使用自定义ZIP处理而非Android官方工具
4. **复杂的构建流程**：多次解压、修改、重新打包，效率低

### 重构方案

#### 1. EmbeddedApkBuilder - 内嵌式构建器
- **统一签名方案**：只使用ZipSigner（已支持Android R+）
- **正确的资源处理**：resources.arsc使用STORED方法，自动4字节对齐
- **简化流程**：减少中间步骤，直接从模板APK到最终APK
- **更好的错误处理**：详细的日志和异常信息

#### 2. 关键改进点

##### Android R+兼容性
```kotlin
// 特殊处理resources.arsc文件
if (entryName == "resources.arsc") {
    entry.method = ZipEntry.STORED  // 未压缩
    val fileBytes = file.readBytes()
    entry.size = fileBytes.size.toLong()
    
    // 计算CRC32
    val crc32 = CRC32()
    crc32.update(fileBytes)
    entry.crc = crc32.value
}
```

##### 统一签名流程
```kotlin
// 使用ZipSigner统一签名，支持Android R+
ZipSigner.signZip(
    certificate,
    privateKey,
    "SHA256withRSA",  // 使用更安全的SHA256
    tempApkFile.absolutePath,
    outputApk.absolutePath
)
```

##### 优化的文件处理
```kotlin
// 直接在ZIP流中处理文件，减少I/O操作
ZipOutputStream(FileOutputStream(tempApkFile)).use { zipOut ->
    zipOut.setLevel(9)  // 最高压缩级别
    workspaceDir.walkTopDown().forEach { file ->
        if (file.isFile && file != tempApkFile) {
            val relativePath = file.relativeTo(workspaceDir).path.replace('\\', '/')
            addFileToZip(zipOut, file, relativePath)
        }
    }
}
```

## 构建流程对比

### 原有流程
1. 解压模板APK → 工作目录
2. 修改AndroidManifest.xml
3. 复制脚本文件
4. 使用ZipOutputStream重新打包
5. 使用多种签名方案之一签名

### 新流程
1. 解压模板APK → 工作目录
2. 修改AndroidManifest.xml和资源
3. 复制脚本文件
4. **优化打包**：正确处理Android R+要求
5. **统一签名**：使用ZipSigner签名

## 使用方法

### 在BuildViewModel中集成
```kotlin
// 替换原有的ApkBuilder
val apkBuilder = EmbeddedApkBuilder(app, templateApk, outApk, tmpDir)

apkBuilder
    .prepare()
    .withConfig(config)
    .build()
    .sign(keyStorePath, keyPassword)
```

### 配置要求
1. **模板APK**：确保从正确配置的inrt模块构建
2. **签名证书**：支持用户自定义或默认证书
3. **工作目录**：自动管理临时文件和清理

## 测试验证

### 构建测试
1. 构建一个简单的脚本应用
2. 检查生成的APK结构
3. 验证resources.arsc是否未压缩
4. 在Android 11+设备上安装测试

### 验证命令
```bash
# 检查APK结构
unzip -l app.apk | grep resources.arsc

# 验证签名
apksigner verify --verbose app.apk

# 检查对齐
zipalign -c -v 4 app.apk
```

## 优势

1. **兼容性**：完全支持Android R+ (API 30+) 要求
2. **可靠性**：使用经过验证的ZipSigner签名方案
3. **性能**：减少中间步骤，提高构建速度
4. **维护性**：统一的代码结构，易于维护和扩展
5. **错误处理**：更好的异常处理和用户反馈

## 后续优化

1. **并行处理**：文件复制和资源处理可以并行化
2. **缓存机制**：模板APK解压结果可以缓存
3. **增量构建**：只重新处理修改的文件
4. **资源优化**：自动压缩图片和资源文件

## 迁移指南

1. **备份现有代码**：保留原有ApkBuilder作为备用
2. **逐步迁移**：先在测试环境使用EmbeddedApkBuilder
3. **验证功能**：确保所有构建功能正常工作
4. **性能测试**：对比构建速度和APK质量
5. **用户反馈**：收集用户使用反馈，持续优化
